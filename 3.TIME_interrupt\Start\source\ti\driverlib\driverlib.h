/*
 * Copyright (c) 2020, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef ti_dl_dl_driverlib__include
#define ti_dl_dl_driverlib__include

#include <ti/driverlib/dl_adc12.h>
#include <ti/driverlib/dl_aes.h>
#include <ti/driverlib/dl_aesadv.h>
#include <ti/driverlib/dl_common.h>
#include <ti/driverlib/dl_comp.h>
#include <ti/driverlib/dl_crc.h>
#include <ti/driverlib/dl_crcp.h>
#include <ti/driverlib/dl_dac12.h>
#include <ti/driverlib/dl_dma.h>
#include <ti/driverlib/dl_flashctl.h>
#include <ti/driverlib/dl_gpamp.h>
#include <ti/driverlib/dl_gpio.h>
#include <ti/driverlib/dl_i2c.h>
#include <ti/driverlib/dl_iwdt.h>
#include <ti/driverlib/dl_keystorectl.h>
#include <ti/driverlib/dl_lcd.h>
#include <ti/driverlib/dl_lfss.h>
#include <ti/driverlib/dl_mathacl.h>
#include <ti/driverlib/dl_mcan.h>
#include <ti/driverlib/dl_opa.h>
#include <ti/driverlib/dl_rtc.h>
#include <ti/driverlib/dl_rtc_a.h>
#include <ti/driverlib/dl_rtc_common.h>
#include <ti/driverlib/dl_scratchpad.h>
#include <ti/driverlib/dl_spi.h>
#include <ti/driverlib/dl_tamperio.h>
#include <ti/driverlib/dl_timera.h>
#include <ti/driverlib/dl_timerg.h>
#include <ti/driverlib/dl_trng.h>
#include <ti/driverlib/dl_uart_extend.h>
#include <ti/driverlib/dl_uart_main.h>
#include <ti/driverlib/dl_vref.h>
#include <ti/driverlib/dl_wwdt.h>
#include <ti/driverlib/m0p/dl_factoryregion.h>
#include <ti/driverlib/m0p/dl_interrupt.h>
#include <ti/driverlib/m0p/dl_sysctl.h>
#include <ti/driverlib/m0p/dl_systick.h>

#endif /* ti_dl_dl_driverlib__include */
