%%{
    /*
     * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
     * All rights reserved.
     *
     * Redistribution and use in source and binary forms, with or without
     * modification, are permitted provided that the following conditions
     * are met:
     *
     * *  Redistributions of source code must retain the above copyright
     *    notice, this list of conditions and the following disclaimer.
     *
     * *  Redistributions in binary form must reproduce the above copyright
     *    notice, this list of conditions and the following disclaimer in the
     *    documentation and/or other materials provided with the distribution.
     *
     * *  Neither the name of Texas Instruments Incorporated nor the names of
     *    its contributors may be used to endorse or promote products derived
     *    from this software without specific prior written permission.
     *
     * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
     * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
     * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
     * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
     * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
     * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
     * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
     * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
     * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
     * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
     * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     *
     */

    /*
     *  ======== DMA.Board.h.xdt ========
     */

    let DMA = args[0];
    let content = args[1];

    /* shorthand names for some common references in template below */
    let inst = DMA.$static;
    if (inst.length == 0) return;

    /* instances represents the dma channels */
    let dmaChanMod = system.modules["/ti/driverlib/DMAChannel"];
    let instances = [];
    if(!(dmaChanMod === undefined)){
        instances = dmaChanMod.$instances;
    }

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let mapping = require('./DMA_TriggerMapping');

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            return;
    }

%%}
%
% function printDefine() {
%   let ownedCollection = {};
%%{
    let ownedCollectionTotal = {};
    for (let i in instances) {
        let inst = instances[i];
        let ownedBy = inst.$ownedBy;
        if(ownedBy !== undefined && inst.$ownedBy.$name !== "/ti/driverlib/DMA") {
            let parentPeripheral;
            if(inst.$ownedBy.peripheral !== undefined){
                parentPeripheral = inst.$ownedBy.peripheral.$solution.peripheralName;
            }
            else{
                parentPeripheral = inst.$ownedBy.$name;
            }
                if(ownedCollectionTotal[parentPeripheral] !== undefined){
                ownedCollectionTotal[parentPeripheral] = ownedCollectionTotal[parentPeripheral]+1;
            }
            else{
                ownedCollectionTotal[parentPeripheral] = 0;
            }
        }
    }
%%}
%   for (let i in instances) {
%       let inst = instances[i];
%       let ownedBy = inst.$ownedBy;
%       let nameStr = "#define "+inst.$name+"_INST"
/* Defines for `inst.$name` */
%
%       let chanIDStr = "#define "+inst.$name+"_CHAN_ID";
%       let chanIDNum = "("+inst.channelID+")";
`chanIDStr.padEnd(40," ")+chanIDNum.padStart(40," ")`
%
%       let peripheralTriggerString = "";
%       let peripheralTriggerSource = ""
%       if(ownedBy !== undefined && inst.$ownedBy.$name !== "/ti/driverlib/DMA") {
%%{
            let parentPeripheral;
            if(inst.$ownedBy.peripheral !== undefined){
                parentPeripheral = inst.$ownedBy.peripheral.$solution.peripheralName;
                peripheralTriggerString = "#define " + (inst.$ownedBy.$name).replace("/ti/driverlib/","") + "_INST_DMA_TRIGGER";
            }
            /* case for static modules */
            else{
                parentPeripheral = inst.$ownedBy.$name;
                peripheralTriggerString = "#define " + (inst.$ownedBy.$name).replace("/ti/driverlib/","") + "_DMA_TRIGGER";
            }
%%}
%%{
    if(ownedCollection[parentPeripheral] !== undefined){
        ownedCollection[parentPeripheral] = ownedCollection[parentPeripheral]+1;
    }
    else{
        ownedCollection[parentPeripheral] = 0;
    }
%%}
%%{
    let inheritedTrigger;
    if (["/ti/driverlib/CRC","/ti/driverlib/CRCP"].includes(parentPeripheral)){
        inheritedTrigger = "DMA_SOFTWARE_TRIG";
    }
    else{
        inheritedTrigger = mapping.dma_triggers[parentPeripheral.replace("/ti/driverlib/","")][inst.passedTriggerSelect];
        if(inheritedTrigger === undefined){ throw "undefined trigger"; }
    }
%%}
%%{
    if(ownedCollectionTotal[parentPeripheral] !== undefined){
        if(ownedCollectionTotal[parentPeripheral] >0){
            peripheralTriggerString += ("_"+ownedCollection[parentPeripheral]);
        }
    }
%%}
%           peripheralTriggerSource = "("+inheritedTrigger+")";
%       } else {
%           peripheralTriggerString = "#define "+inst.$name+"_TRIGGER_SEL_"+inst.triggerSelect;
%           peripheralTriggerSource = "("+inst.triggerNumber+")";
%       }
`peripheralTriggerString.padEnd(45," ")+peripheralTriggerSource.padStart(35," ")`

% } // for let i in instances
%
% } // function printDefine
%
% function printDeclare() {
void SYSCFG_DL_DMA_init(void);
% }
