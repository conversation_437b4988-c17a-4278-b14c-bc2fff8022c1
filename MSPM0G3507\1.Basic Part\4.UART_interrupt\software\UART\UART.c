#include "uart.h"

// 全局变量定义
uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
volatile uint8_t uart_rx_index = 0;
volatile uint8_t uart_rx_complete = 0;
static volatile uint8_t uart_current_mode = UART_MODE_RX;
/******************************************************************************************/
/* �������´���, ֧��printf����, ������Ҫѡ��use MicroLIB */

#if 1

#if (__ARMCC_VERSION >= 6010050)            /* ʹ��AC6������ʱ */
__asm(".global __use_no_semihosting\n\t");  /* ������ʹ�ð�����ģʽ */
__asm(".global __ARM_use_no_argv \n\t");    /* AC6����Ҫ����main����Ϊ�޲�����ʽ�����򲿷����̿��ܳ��ְ�����ģʽ */

#else
/* ʹ��AC5������ʱ, Ҫ�����ﶨ��__FILE �� ��ʹ�ð�����ģʽ */
#pragma import(__use_no_semihosting)

struct __FILE
{
    int handle;
    /* Whatever you require here. If the only file you are using is */
    /* standard output using printf() for debugging, no file handling */
    /* is required. */
};

#endif

/* ��ʹ�ð�����ģʽ��������Ҫ�ض���_ttywrch\_sys_exit\_sys_command_string����,��ͬʱ����AC6��AC5ģʽ */
int _ttywrch(int ch)
{
    ch = ch;
    return ch;
}

/* ����_sys_exit()�Ա���ʹ�ð�����ģʽ */
void _sys_exit(int x)
{
    x = x;
}

char *_sys_command_string(char *cmd, int len)
{
    return NULL;
}


/* FILE �� stdio.h���涨��. */
FILE __stdout;

/* MDK����Ҫ�ض���fputc����, printf�������ջ�ͨ������fputc����ַ��������� */
int fputc(int ch, FILE *f)
{
    UART_SetMode(UART_MODE_TX);  // 切换到发送模式
    DL_UART_Main_transmitDataBlocking(UART_0_INST, (uint8_t)ch);
    UART_SetMode(UART_MODE_RX);  // 切换回接收模式
    return ch;
}
#endif
void UART0_Text(void)
{
    uint8_t send_test[13]={"hello world!\n"};
//    uint8_t *p={"hello world!\n"};
    UART0_Send_Bytes(send_test,sizeof(send_test));
//    UART0_Send_Bytes(p,sizeof(p));
}


void UART0_Send_Bytes(uint8_t *buf, int len)
{
  while(len--)
  {
    DL_UART_Main_transmitDataBlocking(UART_0_INST, *buf);
    buf++;
  }
}


void UART_Init(void)
{
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);

    // 初始化接收缓冲区
    UART_ClearRxBuffer();

    // 设置为接收模式
    UART_SetMode(UART_MODE_RX);
}


void UART_0_INST_IRQHandler(void)
{
    if(DL_UART_Main_getPendingInterrupt(UART_0_INST)==DL_UART_MAIN_IIDX_RX)
    {
        uint8_t data = DL_UART_Main_receiveData(UART_0_INST);

        // 将数据存储到缓冲区
        if(uart_rx_index < UART_RX_BUFFER_SIZE - 1)
        {
            uart_rx_buffer[uart_rx_index] = data;
            uart_rx_index++;
        }

        // 简单的帧结束检测（可根据协议调整）
        if(uart_rx_index >= 6)  // 舵机指令最少6字节
        {
            uart_rx_complete = 1;
        }
    }
}

/**
 * @brief 设置UART半双工模式
 * @param mode: UART_MODE_TX 或 UART_MODE_RX
 */
void UART_SetMode(uint8_t mode)
{
    uart_current_mode = mode;
    // 如果需要硬件控制方向引脚，在这里添加GPIO控制代码
    // if(mode == UART_MODE_TX) {
    //     DL_GPIO_setPins(UART_DIR_PORT, UART_DIR_PIN);
    // } else {
    //     DL_GPIO_clearPins(UART_DIR_PORT, UART_DIR_PIN);
    // }
}

/**
 * @brief 发送单个字节
 * @param data: 要发送的字节
 */
void UART_SendByte(uint8_t data)
{
    UART_SetMode(UART_MODE_TX);
    DL_UART_Main_transmitDataBlocking(UART_0_INST, data);
    UART_SetMode(UART_MODE_RX);
}

/**
 * @brief 发送多个字节
 * @param buf: 数据缓冲区指针
 * @param len: 数据长度
 */
void UART_SendBytes(uint8_t *buf, uint8_t len)
{
    UART_SetMode(UART_MODE_TX);
    for(uint8_t i = 0; i < len; i++)
    {
        DL_UART_Main_transmitDataBlocking(UART_0_INST, buf[i]);
    }
    UART_SetMode(UART_MODE_RX);
}

/**
 * @brief 接收单个字节（阻塞方式）
 * @return 接收到的字节
 */
uint8_t UART_ReceiveByte(void)
{
    UART_SetMode(UART_MODE_RX);
    while(!DL_UART_Main_isRXFIFOEmpty(UART_0_INST) == false);
    return DL_UART_Main_receiveData(UART_0_INST);
}

/**
 * @brief 接收多个字节（带超时）
 * @param buf: 接收缓冲区
 * @param max_len: 最大接收长度
 * @param timeout_ms: 超时时间（毫秒）
 * @return 实际接收到的字节数
 */
uint8_t UART_ReceiveBytes(uint8_t *buf, uint8_t max_len, uint16_t timeout_ms)
{
    uint8_t count = 0;
    uint16_t timeout_count = 0;

    UART_SetMode(UART_MODE_RX);
    UART_ClearRxBuffer();

    while(count < max_len && timeout_count < timeout_ms)
    {
        if(!DL_UART_Main_isRXFIFOEmpty(UART_0_INST))
        {
            buf[count] = DL_UART_Main_receiveData(UART_0_INST);
            count++;
            timeout_count = 0;  // 重置超时计数
        }
        else
        {
            // 简单延时1ms
            for(volatile uint32_t i = 0; i < 32000; i++);
            timeout_count++;
        }
    }

    return count;
}

/**
 * @brief 清空接收缓冲区
 */
void UART_ClearRxBuffer(void)
{
    uart_rx_index = 0;
    uart_rx_complete = 0;
    for(uint8_t i = 0; i < UART_RX_BUFFER_SIZE; i++)
    {
        uart_rx_buffer[i] = 0;
    }
}
