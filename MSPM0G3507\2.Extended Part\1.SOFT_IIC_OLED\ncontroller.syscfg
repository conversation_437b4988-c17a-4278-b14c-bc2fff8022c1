/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER         = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1        = TIMER.addInstance();
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                          = "LED";
GPIO1.associatedPins.create(3);
GPIO1.associatedPins[0].$name        = "LEDR";
GPIO1.associatedPins[0].assignedPin  = "26";
GPIO1.associatedPins[0].assignedPort = "PORTB";
GPIO1.associatedPins[1].$name        = "LEDG";
GPIO1.associatedPins[1].assignedPort = "PORTB";
GPIO1.associatedPins[1].assignedPin  = "27";
GPIO1.associatedPins[2].$name        = "LEDB";
GPIO1.associatedPins[2].assignedPort = "PORTB";
GPIO1.associatedPins[2].assignedPin  = "22";

const Board                       = scripting.addModule("/ti/driverlib/Board", {}, false);
Board.peripheral.$assign          = "DEBUGSS";
Board.peripheral.swclkPin.$assign = "PA20";
Board.peripheral.swdioPin.$assign = "PA19";

GPIO2.$name                               = "KEY";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].$name             = "KEY0";
GPIO2.associatedPins[0].assignedPort      = "PORTA";
GPIO2.associatedPins[0].assignedPin       = "18";
GPIO2.associatedPins[0].direction         = "INPUT";
GPIO2.associatedPins[1].$name             = "KEY1";
GPIO2.associatedPins[1].assignedPort      = "PORTB";
GPIO2.associatedPins[1].assignedPin       = "21";
GPIO2.associatedPins[1].direction         = "INPUT";
GPIO2.associatedPins[1].interruptEn       = true;
GPIO2.associatedPins[1].polarity          = "RISE";
GPIO2.associatedPins[1].internalResistor  = "PULL_UP";
GPIO2.associatedPins[1].interruptPriority = "0";

GPIO3.$name                          = "IIC";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name        = "SCL";
GPIO3.associatedPins[0].assignedPort = "PORTB";
GPIO3.associatedPins[0].assignedPin  = "14";
GPIO3.associatedPins[1].$name        = "SDA";
GPIO3.associatedPins[1].assignedPort = "PORTA";
GPIO3.associatedPins[1].assignedPin  = "30";

SYSCTL.clockTreeEn = true;

TIMER1.$name            = "TIMER_0";
TIMER1.timerClkPrescale = 100;
TIMER1.timerMode        = "PERIODIC_UP";
TIMER1.interrupts       = ["ZERO"];
TIMER1.timerPeriod      = "20ms";

UART1.$name                    = "UART_0";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

ProjectConfig.deviceSpin = "MSPM0G3507";
scripting.suppress("Migrating requires going through the Switch Board or Device menu in the Device View section\\.", ProjectConfig, "deviceSpin");

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
GPIO1.associatedPins[0].pin.$suggestSolution = "PB26";
GPIO1.associatedPins[1].pin.$suggestSolution = "PB27";
GPIO1.associatedPins[2].pin.$suggestSolution = "PB22";
GPIO2.associatedPins[0].pin.$suggestSolution = "PA18";
GPIO2.associatedPins[1].pin.$suggestSolution = "PB21";
GPIO3.associatedPins[0].pin.$suggestSolution = "PB14";
GPIO3.associatedPins[1].pin.$suggestSolution = "PA30";
TIMER1.peripheral.$suggestSolution           = "TIMA0";
UART1.peripheral.$suggestSolution            = "UART0";
