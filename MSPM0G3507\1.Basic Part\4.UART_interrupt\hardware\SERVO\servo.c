#include "servo.h"

/**
 * @brief 舵机初始化
 */
void Servo_Init(void)
{
    // UART已在主函数中初始化，这里可以添加舵机特定的初始化代码
    // 延时等待舵机上电稳定
    for(volatile uint32_t i = 0; i < 3200000; i++);  // 约100ms延时
}

/**
 * @brief 角度转换为位置值
 * @param angle: 角度值 (0-240度)
 * @return 位置值 (0-1000)
 */
uint16_t Servo_AngleToPosition(float angle)
{
    if (angle < SERVO_MIN_ANGLE) angle = SERVO_MIN_ANGLE;
    if (angle > SERVO_MAX_ANGLE) angle = SERVO_MAX_ANGLE;
    return (uint16_t)(angle * SERVO_MAX_POSITION / SERVO_MAX_ANGLE);
}

/**
 * @brief 位置值转换为角度
 * @param position: 位置值 (0-1000)
 * @return 角度值 (0-240度)
 */
float Servo_PositionToAngle(uint16_t position)
{
    if (position > SERVO_MAX_POSITION) position = SERVO_MAX_POSITION;
    return (float)(position * SERVO_MAX_ANGLE / SERVO_MAX_POSITION);
}

/**
 * @brief 计算校验和
 * @param data: 数据指针
 * @param length: 数据长度
 * @return 校验和
 */
uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length)
{
    uint16_t sum = 0;
    for (uint8_t i = 0; i < length; i++) {
        sum += data[i];
    }
    return (~sum) & 0xFF;
}

/**
 * @brief 发送舵机指令
 * @param id: 舵机ID
 * @param cmd: 指令码
 * @param params: 参数数组
 * @param param_len: 参数长度
 */
void Servo_SendCommand(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len)
{
    uint8_t buffer[16];
    uint8_t index = 0;

    // 帧头
    buffer[index++] = SERVO_HEADER;
    buffer[index++] = SERVO_HEADER;

    // ID
    buffer[index++] = id;

    // 长度 = Cmd + Params + Checksum
    buffer[index++] = param_len + 3;  // 修正长度计算

    // 指令码
    buffer[index++] = cmd;

    // 参数部分
    for (uint8_t i = 0; i < param_len; i++) {
        buffer[index++] = params[i];
    }

    // 校验和（不包含帧头）
    uint8_t checksum = Servo_CalculateChecksum(&buffer[2], param_len + 2);
    buffer[index++] = checksum;

    UART0_Send_Bytes(buffer, index);

    // 发送后延时
    for(volatile uint32_t i = 0; i < 320000; i++);  // 约10ms延时
}

/**
 * @brief 设置舵机位置（默认时间）
 * @param id: 舵机ID
 * @param angle: 目标角度
 */
void Servo_SetPosition(uint8_t id, float angle)
{
    Servo_SetPositionWithTime(id, angle, SERVO_DEFAULT_TIME);
}

/**
 * @brief 设置舵机位置（指定时间）
 * @param id: 舵机ID
 * @param angle: 目标角度
 * @param time_ms: 运动时间（毫秒）
 */
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms)
{
    uint16_t pos = Servo_AngleToPosition(angle);
    uint8_t params[4];
    
    params[0] = pos & 0xFF;
    params[1] = (pos >> 8) & 0xFF;
    params[2] = time_ms & 0xFF;
    params[3] = (time_ms >> 8) & 0xFF;
    
    Servo_SendCommand(id, SERVO_CMD_MOVE_TIME_WRITE, params, 4);
}

/**
 * @brief 读取舵机位置
 * @param id: 舵机ID
 * @return 位置值
 */
uint16_t Servo_ReadPosition(uint8_t id)
{
    uint8_t params[1];  // 修正数组声明
    uint8_t rx_buffer[16];

    Servo_SendCommand(id, SERVO_CMD_POS_READ, params, 0);

    // 简单延时等待响应
    for(volatile uint32_t i = 0; i < 320000; i++);  // 约10ms延时

    // 检查是否有数据接收
    if(uart_rx_complete && uart_rx_index >= 7)
    {
        if(uart_rx_buffer[0] == SERVO_HEADER && uart_rx_buffer[1] == SERVO_HEADER)
        {
            // 解析位置数据
            uint16_t position = uart_rx_buffer[5] | (uart_rx_buffer[6] << 8);
            UART_ClearRxBuffer();  // 清空缓冲区
            return position;
        }
    }

    UART_ClearRxBuffer();  // 清空缓冲区
    return 0;  // 读取失败返回0
}

/**
 * @brief 读取舵机角度
 * @param id: 舵机ID
 * @return 角度值
 */
float Servo_ReadAngle(uint8_t id)
{
    uint16_t position = Servo_ReadPosition(id);
    return Servo_PositionToAngle(position);
}

/**
 * @brief 设置水平舵机角度
 * @param angle: 角度值
 */
void Servo_SetHorizontalAngle(float angle)
{
    Servo_SetPosition(SERVO_ID_HORIZONTAL, angle);
}

/**
 * @brief 设置垂直舵机角度
 * @param angle: 角度值
 */
void Servo_SetVerticalAngle(float angle)
{
    Servo_SetPosition(SERVO_ID_VERTICAL, angle);
}

/**
 * @brief 同时设置两个舵机角度
 * @param horizontal_angle: 水平角度
 * @param vertical_angle: 垂直角度
 */
void Servo_SetBothAngles(float horizontal_angle, float vertical_angle)
{
    Servo_SetHorizontalAngle(horizontal_angle);
    for(volatile uint32_t i = 0; i < 160000; i++);  // 5ms延时
    Servo_SetVerticalAngle(vertical_angle);
}

/**
 * @brief 设置舵机到默认位置
 */
void Servo_SetDefaultPosition(void)
{
    Servo_SetBothAngles(SERVO_DEFAULT_HORIZONTAL_ANGLE, SERVO_DEFAULT_VERTICAL_ANGLE);
}

/**
 * @brief 舵机测试函数
 */
void Servo_Test(void)
{
    // 测试水平舵机
    Servo_SetHorizontalAngle(0);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    Servo_SetHorizontalAngle(120);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    Servo_SetHorizontalAngle(240);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    // 测试垂直舵机
    Servo_SetVerticalAngle(0);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    Servo_SetVerticalAngle(120);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    Servo_SetVerticalAngle(240);
    for(volatile uint32_t i = 0; i < 32000000; i++);  // 1s延时
    
    // 回到默认位置
    Servo_SetDefaultPosition();
}
