#ifndef _UART_H_
#define _UART_H_

#include "A_include.h"

// 半双工模式控制引脚定义（如果需要硬件控制）
#define UART_DIR_PIN    DL_GPIO_PIN_12  // 假设使用PA12作为方向控制引脚
#define UART_DIR_PORT   GPIOA

// 半双工模式定义
#define UART_MODE_TX    1
#define UART_MODE_RX    0

// 接收缓冲区大小
#define UART_RX_BUFFER_SIZE 64

// 全局变量声明
extern uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
extern volatile uint8_t uart_rx_index;
extern volatile uint8_t uart_rx_complete;

// 函数声明
void UART_Init(void);
void UART_SetMode(uint8_t mode);
void UART_SendByte(uint8_t data);
void UART_SendBytes(uint8_t *buf, uint8_t len);
uint8_t UART_ReceiveByte(void);
uint8_t UART_ReceiveBytes(uint8_t *buf, uint8_t max_len, uint16_t timeout_ms);
void UART_ClearRxBuffer(void);
void UART0_Text(void);

// 兼容性函数声明（保持与原代码兼容）
void UART0_Send_Bytes(uint8_t *buf, int len);

#endif

