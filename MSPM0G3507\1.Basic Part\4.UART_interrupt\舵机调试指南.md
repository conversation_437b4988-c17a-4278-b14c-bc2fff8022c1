# 舵机不动问题调试指南

## 已修复的问题

### 1. UART半双工模式切换问题
**问题**: `UART_SetMode`函数只设置变量，没有实际控制硬件
**修复**: 
```c
void UART_SetMode(uint8_t mode)
{
    if(mode == UART_MODE_TX) {
        // 发送模式：禁用接收中断，等待发送完成
        DL_UART_Main_disableInterrupt(UART_0_INST, DL_UART_MAIN_INTERRUPT_RX);
        while(DL_UART_Main_isTXFIFOEmpty(UART_0_INST) == false);
    } else {
        // 接收模式：启用接收中断，清空接收FIFO
        DL_UART_Main_enableInterrupt(UART_0_INST, DL_UART_MAIN_INTERRUPT_RX);
        while(DL_UART_Main_isRXFIFOEmpty(UART_0_INST) == false) {
            DL_UART_Main_receiveData(UART_0_INST);
        }
    }
}
```

### 2. 发送时序优化
**问题**: 发送完成后立即切换模式，可能导致数据丢失
**修复**: 
```c
void UART0_Send_Bytes(uint8_t *buf, int len)
{
    UART_SetMode(UART_MODE_TX);
    
    // 发送所有数据
    while(len--) {
        DL_UART_Main_transmitDataBlocking(UART_0_INST, *buf);
        buf++;
    }
    
    // 等待发送完成
    while(DL_UART_Main_isTXFIFOEmpty(UART_0_INST) == false);
    
    // 额外延时确保数据完全发送
    for(volatile uint32_t i = 0; i < 1600; i++);  // 约50us延时
    
    UART_SetMode(UART_MODE_RX);
}
```

### 3. 舵机协议校验和修正
**问题**: 校验和计算可能不正确
**修复**: 
```c
// 校验和计算（ID + Length + CMD + Params）
uint8_t checksum_data[16];
uint8_t checksum_len = 0;
checksum_data[checksum_len++] = id;
checksum_data[checksum_len++] = param_len + 2;
checksum_data[checksum_len++] = cmd;
for (uint8_t i = 0; i < param_len; i++) {
    checksum_data[checksum_len++] = params[i];
}
uint8_t checksum = Servo_CalculateChecksum(checksum_data, checksum_len);
```

### 4. LED控制逻辑修改
**问题**: 原来是按键切换模式，现在改为按下亮、松开灭
**修复**: 
```c
// KEY0控制LED - 按下亮，松开灭
if(DL_GPIO_readPins(GPIOA, DL_GPIO_PIN_18) == 0)  // KEY0按下（低电平）
{
    LEDR(1);  // 点亮红灯
}
else
{
    LEDR(0);  // 熄灭红灯
}
```

## 调试步骤

### 1. 检查串口输出
连接串口助手（115200bps），查看以下信息：
- 系统启动信息
- 测试指令发送的十六进制数据
- 按键操作的反馈信息

### 2. 验证发送数据格式
正确的舵机指令格式应该是：
```
发送到ID1舵机90度的指令：
55 55 01 06 01 7C 01 E8 03 XX
```
其中：
- `55 55`: 帧头
- `01`: 舵机ID
- `06`: 数据长度 (CMD + 4个参数 + 校验和)
- `01`: 指令码 (SERVO_CMD_MOVE_TIME_WRITE)
- `7C 01`: 位置值375 (90度对应375)
- `E8 03`: 时间1000ms
- `XX`: 校验和

### 3. 硬件连接检查
确认以下连接：
- UART_TX (PA10) 连接到舵机驱动板的信号输入
- UART_RX (PA11) 连接到舵机驱动板的信号输出（如果需要反馈）
- GND 连接
- 舵机供电正常（5V-7.4V）

### 4. 舵机ID设置
确认舵机ID设置正确：
- 水平舵机设置为ID1
- 垂直舵机设置为ID2

## 测试方法

### 1. 基本功能测试
1. 上电后观察串口输出
2. 按下KEY0，红色LED应该点亮
3. 松开KEY0，红色LED应该熄灭
4. 按下KEY1，观察舵机是否运动

### 2. 通信数据验证
1. 使用示波器或逻辑分析仪观察UART信号
2. 验证波特率是否为115200
3. 检查数据帧格式是否正确

### 3. 简单测试指令
系统启动时会自动发送测试指令：
```c
Servo_SimpleTest(SERVO_ID_HORIZONTAL);  // 发送到ID1
Servo_SimpleTest(SERVO_ID_VERTICAL);    // 发送到ID2
```

## 可能的问题和解决方案

### 1. 舵机完全无响应
- 检查供电是否正常
- 确认UART连接是否正确
- 验证舵机ID是否匹配
- 检查波特率设置

### 2. 舵机有响应但角度不对
- 检查角度转换公式
- 验证位置值计算是否正确
- 确认舵机的角度范围设置

### 3. 通信不稳定
- 增加发送延时
- 检查半双工切换时序
- 验证校验和计算

### 4. 只有一个舵机响应
- 检查舵机ID设置
- 确认两个舵机的供电和连接
- 验证发送间隔是否足够

## 调试输出示例

正常工作时的串口输出应该类似：
```
MSPM0G3507 Servo Control System Started
UART: 115200bps, Half-Duplex Mode
KEY0: LED Control (Press to light, Release to off)
KEY1: Servo Control (ID1:90deg, ID2:120deg)
Sending test commands...
Simple test for Servo ID1
Send to Servo ID1: 55 55 01 06 01 F4 01 E8 03 XX
Simple test for Servo ID2
Send to Servo ID2: 55 55 02 06 01 F4 01 E8 03 XX
Test commands sent!
```

如果舵机仍然不动，请检查：
1. 舵机驱动板是否支持该协议
2. 舵机是否已正确设置ID
3. 硬件连接是否正确
4. 供电是否稳定
