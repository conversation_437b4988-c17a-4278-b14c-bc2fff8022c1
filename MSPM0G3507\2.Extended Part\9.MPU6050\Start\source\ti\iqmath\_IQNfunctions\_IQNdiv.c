#include "_IQNdiv.h"

/* RTS Functions */
#if ((!defined (__IQMATH_USE_MATHACL__)) || (!defined (__MSPM0_HAS_MATHACL__)))
/**
 * @brief Divides two values of IQ31 format.
 *
 * @param a             IQ31 type value numerator to be divided.
 * @param b             IQ31 type value denominator to divide by.
 *
 * @return              IQ31 type result of the multiplication. 
 */
int32_t _IQ31div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 31);
}
/**
 * @brief Divides two values of IQ30 format.
 *
 * @param a             IQ30 type value numerator to be divided.
 * @param b             IQ30 type value denominator to divide by.
 *
 * @return              IQ30 type result of the multiplication. 
 */
int32_t _IQ30div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 30);
}
/**
 * @brief Divides two values of IQ29 format.
 *
 * @param a             IQ29 type value numerator to be divided.
 * @param b             IQ29 type value denominator to divide by.
 *
 * @return              IQ29 type result of the multiplication. 
 */
int32_t _IQ29div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 29);
}
/**
 * @brief Divides two values of IQ28 format.
 *
 * @param a             IQ28 type value numerator to be divided.
 * @param b             IQ28 type value denominator to divide by.
 *
 * @return              IQ28 type result of the multiplication. 
 */
int32_t _IQ28div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 28);
}
/**
 * @brief Divides two values of IQ27 format.
 *
 * @param a             IQ27 type value numerator to be divided.
 * @param b             IQ27 type value denominator to divide by.
 *
 * @return              IQ27 type result of the multiplication. 
 */
int32_t _IQ27div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 27);
}
/**
 * @brief Divides two values of IQ26 format.
 *
 * @param a             IQ26 type value numerator to be divided.
 * @param b             IQ26 type value denominator to divide by.
 *
 * @return              IQ26 type result of the multiplication. 
 */
int32_t _IQ26div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 26);
}
/**
 * @brief Divides two values of IQ25 format.
 *
 * @param a             IQ25 type value numerator to be divided.
 * @param b             IQ25 type value denominator to divide by.
 *
 * @return              IQ25 type result of the multiplication. 
 */
int32_t _IQ25div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 25);
}
/**
 * @brief Divides two values of IQ24 format.
 *
 * @param a             IQ24 type value numerator to be divided.
 * @param b             IQ24 type value denominator to divide by.
 *
 * @return              IQ24 type result of the multiplication. 
 */
int32_t _IQ24div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 24);
}
/**
 * @brief Divides two values of IQ23 format.
 *
 * @param a             IQ23 type value numerator to be divided.
 * @param b             IQ23 type value denominator to divide by.
 *
 * @return              IQ23 type result of the multiplication. 
 */
int32_t _IQ23div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 23);
}
/**
 * @brief Divides two values of IQ22 format.
 *
 * @param a             IQ22 type value numerator to be divided.
 * @param b             IQ22 type value denominator to divide by.
 *
 * @return              IQ22 type result of the multiplication. 
 */
int32_t _IQ22div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 22);
}
/**
 * @brief Divides two values of IQ21 format.
 *
 * @param a             IQ21 type value numerator to be divided.
 * @param b             IQ21 type value denominator to divide by.
 *
 * @return              IQ21 type result of the multiplication. 
 */
int32_t _IQ21div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 21);
}
/**
 * @brief Divides two values of IQ20 format.
 *
 * @param a             IQ20 type value numerator to be divided.
 * @param b             IQ20 type value denominator to divide by.
 *
 * @return              IQ20 type result of the multiplication. 
 */
int32_t _IQ20div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 20);
}
/**
 * @brief Divides two values of IQ19 format.
 *
 * @param a             IQ19 type value numerator to be divided.
 * @param b             IQ19 type value denominator to divide by.
 *
 * @return              IQ19 type result of the multiplication. 
 */
int32_t _IQ19div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 19);
}
/**
 * @brief Divides two values of IQ18 format.
 *
 * @param a             IQ18 type value numerator to be divided.
 * @param b             IQ18 type value denominator to divide by.
 *
 * @return              IQ18 type result of the multiplication. 
 */
int32_t _IQ18div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 18);
}
/**
 * @brief Divides two values of IQ17 format.
 *
 * @param a             IQ17 type value numerator to be divided.
 * @param b             IQ17 type value denominator to divide by.
 *
 * @return              IQ17 type result of the multiplication. 
 */
int32_t _IQ17div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 17);
}
/**
 * @brief Divides two values of IQ16 format.
 *
 * @param a             IQ16 type value numerator to be divided.
 * @param b             IQ16 type value denominator to divide by.
 *
 * @return              IQ16 type result of the multiplication. 
 */
int32_t _IQ16div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 16);
}
/**
 * @brief Divides two values of IQ15 format.
 *
 * @param a             IQ15 type value numerator to be divided.
 * @param b             IQ15 type value denominator to divide by.
 *
 * @return              IQ15 type result of the multiplication. 
 */
int32_t _IQ15div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 15);
}
/**
 * @brief Divides two values of IQ14 format.
 *
 * @param a             IQ14 type value numerator to be divided.
 * @param b             IQ14 type value denominator to divide by.
 *
 * @return              IQ14 type result of the multiplication. 
 */
int32_t _IQ14div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 14);
}
/**
 * @brief Divides two values of IQ13 format.
 *
 * @param a             IQ13 type value numerator to be divided.
 * @param b             IQ13 type value denominator to divide by.
 *
 * @return              IQ13 type result of the multiplication. 
 */
int32_t _IQ13div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 13);
}
/**
 * @brief Divides two values of IQ12 format.
 *
 * @param a             IQ12 type value numerator to be divided.
 * @param b             IQ12 type value denominator to divide by.
 *
 * @return              IQ12 type result of the multiplication. 
 */
int32_t _IQ12div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 12);
}
/**
 * @brief Divides two values of IQ11 format.
 *
 * @param a             IQ11 type value numerator to be divided.
 * @param b             IQ11 type value denominator to divide by.
 *
 * @return              IQ11 type result of the multiplication. 
 */
int32_t _IQ11div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 11);
}
/**
 * @brief Divides two values of IQ10 format.
 *
 * @param a             IQ10 type value numerator to be divided.
 * @param b             IQ10 type value denominator to divide by.
 *
 * @return              IQ10 type result of the multiplication. 
 */
int32_t _IQ10div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 10);
}
/**
 * @brief Divides two values of IQ9 format.
 *
 * @param a             IQ9 type value numerator to be divided.
 * @param b             IQ9 type value denominator to divide by.
 *
 * @return              IQ9 type result of the multiplication. 
 */
int32_t _IQ9div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 9);
}
/**
 * @brief Divides two values of IQ8 format.
 *
 * @param a             IQ8 type value numerator to be divided.
 * @param b             IQ8 type value denominator to divide by.
 *
 * @return              IQ8 type result of the multiplication. 
 */
int32_t _IQ8div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 8);
}
/**
 * @brief Divides two values of IQ7 format.
 *
 * @param a             IQ7 type value numerator to be divided.
 * @param b             IQ7 type value denominator to divide by.
 *
 * @return              IQ7 type result of the multiplication. 
 */
int32_t _IQ7div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 7);
}
/**
 * @brief Divides two values of IQ6 format.
 *
 * @param a             IQ6 type value numerator to be divided.
 * @param b             IQ6 type value denominator to divide by.
 *
 * @return              IQ6 type result of the multiplication. 
 */
int32_t _IQ6div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 6);
}
/**
 * @brief Divides two values of IQ5 format.
 *
 * @param a             IQ5 type value numerator to be divided.
 * @param b             IQ5 type value denominator to divide by.
 *
 * @return              IQ5 type result of the multiplication. 
 */
int32_t _IQ5div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 5);
}
/**
 * @brief Divides two values of IQ4 format.
 *
 * @param a             IQ4 type value numerator to be divided.
 * @param b             IQ4 type value denominator to divide by.
 *
 * @return              IQ4 type result of the multiplication. 
 */
int32_t _IQ4div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 4);
}
/**
 * @brief Divides two values of IQ3 format.
 *
 * @param a             IQ3 type value numerator to be divided.
 * @param b             IQ3 type value denominator to divide by.
 *
 * @return              IQ3 type result of the multiplication. 
 */
int32_t _IQ3div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 3);
}
/**
 * @brief Divides two values of IQ2 format.
 *
 * @param a             IQ2 type value numerator to be divided.
 * @param b             IQ2 type value denominator to divide by.
 *
 * @return              IQ2 type result of the multiplication. 
 */
int32_t _IQ2div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 2);
}
/**
 * @brief Divides two values of IQ1 format.
 *
 * @param a             IQ1 type value numerator to be divided.
 * @param b             IQ1 type value denominator to divide by.
 *
 * @return              IQ1 type result of the multiplication. 
 */
int32_t _IQ1div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 1);
}
// TODO: this was in MSP430, but I see no use for it. there is no IQ0
/**
 * @brief Divides two values of IQ0 format.
 *
 * @param a             IQ0 type value numerator to be divided.
 * @param b             IQ0 type value denominator to divide by.
 *
 * @return              IQ0 type result of the multiplication. 
 */
int32_t _IQ0div(int32_t a, int32_t b)
{
    return __IQNdiv(a, b, TYPE_DEFAULT, 0);
}
/* MathACL Functions */
#else
/**
 * @brief Divides two values of IQ31 format, using MathACL
 *
 * @param a             IQ31 type value numerator to be divided.
 * @param b             IQ31 type value denominator to divide by.
 *
 * @return              IQ31 type result of the multiplication. 
 */
int32_t _IQ31div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 31);
}
/**
 * @brief Divides two values of IQ30 format, using MathACL
 *
 * @param a             IQ30 type value numerator to be divided.
 * @param b             IQ30 type value denominator to divide by.
 *
 * @return              IQ30 type result of the multiplication. 
 */
int32_t _IQ30div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 30);
}
/**
 * @brief Divides two values of IQ29 format, using MathACL
 *
 * @param a             IQ29 type value numerator to be divided.
 * @param b             IQ29 type value denominator to divide by.
 *
 * @return              IQ29 type result of the multiplication. 
 */
int32_t _IQ29div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 29);
}
/**
 * @brief Divides two values of IQ28 format, using MathACL
 *
 * @param a             IQ28 type value numerator to be divided.
 * @param b             IQ28 type value denominator to divide by.
 *
 * @return              IQ28 type result of the multiplication. 
 */
int32_t _IQ28div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 28);
}
/**
 * @brief Divides two values of IQ27 format, using MathACL
 *
 * @param a             IQ27 type value numerator to be divided.
 * @param b             IQ27 type value denominator to divide by.
 *
 * @return              IQ27 type result of the multiplication. 
 */
int32_t _IQ27div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 27);
}
/**
 * @brief Divides two values of IQ26 format, using MathACL
 *
 * @param a             IQ26 type value numerator to be divided.
 * @param b             IQ26 type value denominator to divide by.
 *
 * @return              IQ26 type result of the multiplication. 
 */
int32_t _IQ26div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 26);
}
/**
 * @brief Divides two values of IQ25 format, using MathACL
 *
 * @param a             IQ25 type value numerator to be divided.
 * @param b             IQ25 type value denominator to divide by.
 *
 * @return              IQ25 type result of the multiplication. 
 */
int32_t _IQ25div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 25);
}
/**
 * @brief Divides two values of IQ24 format, using MathACL
 *
 * @param a             IQ24 type value numerator to be divided.
 * @param b             IQ24 type value denominator to divide by.
 *
 * @return              IQ24 type result of the multiplication. 
 */
int32_t _IQ24div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 24);
}
/**
 * @brief Divides two values of IQ23 format, using MathACL
 *
 * @param a             IQ23 type value numerator to be divided.
 * @param b             IQ23 type value denominator to divide by.
 *
 * @return              IQ23 type result of the multiplication. 
 */
int32_t _IQ23div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 23);
}
/**
 * @brief Divides two values of IQ22 format, using MathACL
 *
 * @param a             IQ22 type value numerator to be divided.
 * @param b             IQ22 type value denominator to divide by.
 *
 * @return              IQ22 type result of the multiplication. 
 */
int32_t _IQ22div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 22);
}
/**
 * @brief Divides two values of IQ21 format, using MathACL
 *
 * @param a             IQ21 type value numerator to be divided.
 * @param b             IQ21 type value denominator to divide by.
 *
 * @return              IQ21 type result of the multiplication. 
 */
int32_t _IQ21div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 21);
}
/**
 * @brief Divides two values of IQ20 format, using MathACL
 *
 * @param a             IQ20 type value numerator to be divided.
 * @param b             IQ20 type value denominator to divide by.
 *
 * @return              IQ20 type result of the multiplication. 
 */
int32_t _IQ20div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 20);
}
/**
 * @brief Divides two values of IQ19 format, using MathACL
 *
 * @param a             IQ19 type value numerator to be divided.
 * @param b             IQ19 type value denominator to divide by.
 *
 * @return              IQ19 type result of the multiplication. 
 */
int32_t _IQ19div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 19);
}
/**
 * @brief Divides two values of IQ18 format, using MathACL
 *
 * @param a             IQ18 type value numerator to be divided.
 * @param b             IQ18 type value denominator to divide by.
 *
 * @return              IQ18 type result of the multiplication. 
 */
int32_t _IQ18div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 18);
}
/**
 * @brief Divides two values of IQ17 format, using MathACL
 *
 * @param a             IQ17 type value numerator to be divided.
 * @param b             IQ17 type value denominator to divide by.
 *
 * @return              IQ17 type result of the multiplication. 
 */
int32_t _IQ17div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 17);
}
/**
 * @brief Divides two values of IQ16 format, using MathACL
 *
 * @param a             IQ16 type value numerator to be divided.
 * @param b             IQ16 type value denominator to divide by.
 *
 * @return              IQ16 type result of the multiplication. 
 */
int32_t _IQ16div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 16);
}
/**
 * @brief Divides two values of IQ15 format, using MathACL
 *
 * @param a             IQ15 type value numerator to be divided.
 * @param b             IQ15 type value denominator to divide by.
 *
 * @return              IQ15 type result of the multiplication. 
 */
int32_t _IQ15div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 15);
}
/**
 * @brief Divides two values of IQ14 format, using MathACL
 *
 * @param a             IQ14 type value numerator to be divided.
 * @param b             IQ14 type value denominator to divide by.
 *
 * @return              IQ14 type result of the multiplication. 
 */
int32_t _IQ14div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 14);
}
/**
 * @brief Divides two values of IQ13 format, using MathACL
 *
 * @param a             IQ13 type value numerator to be divided.
 * @param b             IQ13 type value denominator to divide by.
 *
 * @return              IQ13 type result of the multiplication. 
 */
int32_t _IQ13div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 13);
}
/**
 * @brief Divides two values of IQ12 format, using MathACL
 *
 * @param a             IQ12 type value numerator to be divided.
 * @param b             IQ12 type value denominator to divide by.
 *
 * @return              IQ12 type result of the multiplication. 
 */
int32_t _IQ12div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 12);
}
/**
 * @brief Divides two values of IQ11 format, using MathACL
 *
 * @param a             IQ11 type value numerator to be divided.
 * @param b             IQ11 type value denominator to divide by.
 *
 * @return              IQ11 type result of the multiplication. 
 */
int32_t _IQ11div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 11);
}
/**
 * @brief Divides two values of IQ10 format, using MathACL
 *
 * @param a             IQ10 type value numerator to be divided.
 * @param b             IQ10 type value denominator to divide by.
 *
 * @return              IQ10 type result of the multiplication. 
 */
int32_t _IQ10div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 10);
}
/**
 * @brief Divides two values of IQ9 format, using MathACL
 *
 * @param a             IQ9 type value numerator to be divided.
 * @param b             IQ9 type value denominator to divide by.
 *
 * @return              IQ9 type result of the multiplication. 
 */
int32_t _IQ9div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 9);
}
/**
 * @brief Divides two values of IQ8 format, using MathACL
 *
 * @param a             IQ8 type value numerator to be divided.
 * @param b             IQ8 type value denominator to divide by.
 *
 * @return              IQ8 type result of the multiplication. 
 */
int32_t _IQ8div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 8);
}
/**
 * @brief Divides two values of IQ7 format, using MathACL
 *
 * @param a             IQ7 type value numerator to be divided.
 * @param b             IQ7 type value denominator to divide by.
 *
 * @return              IQ7 type result of the multiplication. 
 */
int32_t _IQ7div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 7);
}
/**
 * @brief Divides two values of IQ6 format, using MathACL
 *
 * @param a             IQ6 type value numerator to be divided.
 * @param b             IQ6 type value denominator to divide by.
 *
 * @return              IQ6 type result of the multiplication. 
 */
int32_t _IQ6div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 6);
}
/**
 * @brief Divides two values of IQ5 format, using MathACL
 *
 * @param a             IQ5 type value numerator to be divided.
 * @param b             IQ5 type value denominator to divide by.
 *
 * @return              IQ5 type result of the multiplication. 
 */
int32_t _IQ5div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 5);
}
/**
 * @brief Divides two values of IQ4 format, using MathACL
 *
 * @param a             IQ4 type value numerator to be divided.
 * @param b             IQ4 type value denominator to divide by.
 *
 * @return              IQ4 type result of the multiplication. 
 */
int32_t _IQ4div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 4);
}
/**
 * @brief Divides two values of IQ3 format, using MathACL
 *
 * @param a             IQ3 type value numerator to be divided.
 * @param b             IQ3 type value denominator to divide by.
 *
 * @return              IQ3 type result of the multiplication. 
 */
int32_t _IQ3div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 3);
}
/**
 * @brief Divides two values of IQ2 format, using MathACL
 *
 * @param a             IQ2 type value numerator to be divided.
 * @param b             IQ2 type value denominator to divide by.
 *
 * @return              IQ2 type result of the multiplication. 
 */
int32_t _IQ2div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 2);
}
/**
 * @brief Divides two values of IQ1 format, using MathACL
 *
 * @param a             IQ1 type value numerator to be divided.
 * @param b             IQ1 type value denominator to divide by.
 *
 * @return              IQ1 type result of the multiplication. 
 */
int32_t _IQ1div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 1);
}
/**
 * @brief Divides two values of IQ0 format, using MathACL
 *
 * @param a             IQ0 type value numerator to be divided.
 * @param b             IQ0 type value denominator to divide by.
 *
 * @return              IQ0 type result of the multiplication. 
 */
int32_t _IQ0div(int32_t a, int32_t b)
{
    return __IQNdiv_MathACL(a ,b, 0);
}
#endif

/* Hidden unsigned uiq31 divide without sign checks, used for atan2. */
/**
 * @brief Divides two values of IQ31 format, without sign checks.
 *
 * @param a             IQ31 type value numerator to be divided.
 * @param b             IQ31 type value denominator to divide by.
 *
 * @return              IQ31 type result of the multiplication. 
 */
uint32_t _UIQ31div(uint32_t a, uint32_t b)
{
    return __IQNdiv(a, b, TYPE_UNSIGNED, 31);
}
