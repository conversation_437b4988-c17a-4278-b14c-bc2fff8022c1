/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
#include <inttypes.h>
#include "metrology_defines.h"
#include "metrology_structs.h"
#include "metrology_setup.h"

/**
 *
 * @brief This FIR table is used to create accurate 90 degree phase shifts 
 */

#if INT_SAMPLES_PER_10_SECONDS == 20000  && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/*!
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 2000 times per second. There are 256 steps in this table. 
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8217 }, /*   0:  4.5000° */
    {   32263,    8281 }, /*   1:  4.4648° */
    {   31765,    8344 }, /*   2:  4.4297° */
    {   31276,    8408 }, /*   3:  4.3945° */
    {   30793,    8472 }, /*   4:  4.3594° */
    {   30318,    8536 }, /*   5:  4.3242° */
    {   29850,    8600 }, /*   6:  4.2891° */
    {   29389,    8663 }, /*   7:  4.2539° */
    {   28935,    8727 }, /*   8:  4.2188° */
    {   28487,    8791 }, /*   9:  4.1836° */
    {   28045,    8855 }, /*  10:  4.1484° */
    {   27610,    8918 }, /*  11:  4.1133° */
    {   27181,    8982 }, /*  12:  4.0781° */
    {   26759,    9046 }, /*  13:  4.0430° */
    {   26342,    9110 }, /*  14:  4.0078° */
    {   25930,    9174 }, /*  15:  3.9727° */
    {   25525,    9237 }, /*  16:  3.9375° */
    {   25125,    9301 }, /*  17:  3.9023° */
    {   24731,    9365 }, /*  18:  3.8672° */
    {   24341,    9429 }, /*  19:  3.8320° */
    {   23957,    9492 }, /*  20:  3.7969° */
    {   23578,    9556 }, /*  21:  3.7617° */
    {   23205,    9620 }, /*  22:  3.7266° */
    {   22836,    9684 }, /*  23:  3.6914° */
    {   22472,    9747 }, /*  24:  3.6562° */
    {   22112,    9811 }, /*  25:  3.6211° */
    {   21757,    9875 }, /*  26:  3.5859° */
    {   21407,    9939 }, /*  27:  3.5508° */
    {   21061,   10002 }, /*  28:  3.5156° */
    {   20720,   10066 }, /*  29:  3.4805° */
    {   20383,   10130 }, /*  30:  3.4453° */
    {   20050,   10194 }, /*  31:  3.4102° */
    {   19721,   10257 }, /*  32:  3.3750° */
    {   19396,   10321 }, /*  33:  3.3398° */
    {   19075,   10385 }, /*  34:  3.3047° */
    {   18758,   10449 }, /*  35:  3.2695° */
    {   18445,   10512 }, /*  36:  3.2344° */
    {   18136,   10576 }, /*  37:  3.1992° */
    {   17830,   10640 }, /*  38:  3.1641° */
    {   17528,   10704 }, /*  39:  3.1289° */
    {   17230,   10767 }, /*  40:  3.0937° */
    {   16935,   10831 }, /*  41:  3.0586° */
    {   16643,   10895 }, /*  42:  3.0234° */
    {   16355,   10958 }, /*  43:  2.9883° */
    {   16070,   11022 }, /*  44:  2.9531° */
    {   15789,   11086 }, /*  45:  2.9180° */
    {   15510,   11150 }, /*  46:  2.8828° */
    {   15235,   11213 }, /*  47:  2.8477° */
    {   14963,   11277 }, /*  48:  2.8125° */
    {   14694,   11341 }, /*  49:  2.7773° */
    {   14428,   11405 }, /*  50:  2.7422° */
    {   14165,   11468 }, /*  51:  2.7070° */
    {   13904,   11532 }, /*  52:  2.6719° */
    {   13647,   11596 }, /*  53:  2.6367° */
    {   13392,   11660 }, /*  54:  2.6016° */
    {   13140,   11723 }, /*  55:  2.5664° */
    {   12891,   11787 }, /*  56:  2.5312° */
    {   12645,   11851 }, /*  57:  2.4961° */
    {   12401,   11915 }, /*  58:  2.4609° */
    {   12159,   11978 }, /*  59:  2.4258° */
    {   11920,   12042 }, /*  60:  2.3906° */
    {   11684,   12106 }, /*  61:  2.3555° */
    {   11450,   12170 }, /*  62:  2.3203° */
    {   11219,   12233 }, /*  63:  2.2852° */
    {   10990,   12297 }, /*  64:  2.2500° */
    {   10763,   12361 }, /*  65:  2.2148° */
    {   10539,   12425 }, /*  66:  2.1797° */
    {   10316,   12488 }, /*  67:  2.1445° */
    {   10096,   12552 }, /*  68:  2.1094° */
    {    9879,   12616 }, /*  69:  2.0742° */
    {    9663,   12680 }, /*  70:  2.0391° */
    {    9450,   12743 }, /*  71:  2.0039° */
    {    9238,   12807 }, /*  72:  1.9687° */
    {    9029,   12871 }, /*  73:  1.9336° */
    {    8822,   12935 }, /*  74:  1.8984° */
    {    8617,   12998 }, /*  75:  1.8633° */
    {    8414,   13062 }, /*  76:  1.8281° */
    {    8212,   13126 }, /*  77:  1.7930° */
    {    8013,   13190 }, /*  78:  1.7578° */
    {    7815,   13254 }, /*  79:  1.7227° */
    {    7620,   13317 }, /*  80:  1.6875° */
    {    7426,   13381 }, /*  81:  1.6523° */
    {    7234,   13445 }, /*  82:  1.6172° */
    {    7044,   13509 }, /*  83:  1.5820° */
    {    6856,   13572 }, /*  84:  1.5469° */
    {    6669,   13636 }, /*  85:  1.5117° */
    {    6484,   13700 }, /*  86:  1.4766° */
    {    6301,   13764 }, /*  87:  1.4414° */
    {    6119,   13828 }, /*  88:  1.4062° */
    {    5939,   13892 }, /*  89:  1.3711° */
    {    5761,   13955 }, /*  90:  1.3359° */
    {    5584,   14019 }, /*  91:  1.3008° */
    {    5409,   14083 }, /*  92:  1.2656° */
    {    5236,   14147 }, /*  93:  1.2305° */
    {    5064,   14211 }, /*  94:  1.1953° */
    {    4893,   14274 }, /*  95:  1.1602° */
    {    4724,   14338 }, /*  96:  1.1250° */
    {    4557,   14402 }, /*  97:  1.0898° */
    {    4391,   14466 }, /*  98:  1.0547° */
    {    4226,   14530 }, /*  99:  1.0195° */
    {    4063,   14594 }, /* 100:  0.9844° */
    {    3901,   14658 }, /* 101:  0.9492° */
    {    3740,   14721 }, /* 102:  0.9141° */
    {    3581,   14785 }, /* 103:  0.8789° */
    {    3424,   14849 }, /* 104:  0.8437° */
    {    3267,   14913 }, /* 105:  0.8086° */
    {    3112,   14977 }, /* 106:  0.7734° */
    {    2958,   15041 }, /* 107:  0.7383° */
    {    2806,   15105 }, /* 108:  0.7031° */
    {    2654,   15169 }, /* 109:  0.6680° */
    {    2504,   15232 }, /* 110:  0.6328° */
    {    2355,   15296 }, /* 111:  0.5977° */
    {    2208,   15360 }, /* 112:  0.5625° */
    {    2061,   15424 }, /* 113:  0.5273° */
    {    1916,   15488 }, /* 114:  0.4922° */
    {    1772,   15552 }, /* 115:  0.4570° */
    {    1629,   15616 }, /* 116:  0.4219° */
    {    1487,   15680 }, /* 117:  0.3867° */
    {    1347,   15744 }, /* 118:  0.3516° */
    {    1207,   15808 }, /* 119:  0.3164° */
    {    1069,   15872 }, /* 120:  0.2812° */
    {     932,   15936 }, /* 121:  0.2461° */
    {     795,   16000 }, /* 122:  0.2109° */
    {     660,   16064 }, /* 123:  0.1758° */
    {     526,   16128 }, /* 124:  0.1406° */
    {     393,   16192 }, /* 125:  0.1055° */
    {     261,   16256 }, /* 126:  0.0703° */
    {     130,   16320 }, /* 127:  0.0352° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -129,   16448 }, /* 129: -0.0352° */
    {    -257,   16512 }, /* 130: -0.0703° */
    {    -384,   16576 }, /* 131: -0.1055° */
    {    -510,   16640 }, /* 132: -0.1406° */
    {    -635,   16704 }, /* 133: -0.1758° */
    {    -760,   16768 }, /* 134: -0.2109° */
    {    -883,   16832 }, /* 135: -0.2461° */
    {   -1006,   16896 }, /* 136: -0.2813° */
    {   -1127,   16960 }, /* 137: -0.3164° */
    {   -1248,   17024 }, /* 138: -0.3516° */
    {   -1368,   17088 }, /* 139: -0.3867° */
    {   -1487,   17152 }, /* 140: -0.4219° */
    {   -1605,   17216 }, /* 141: -0.4570° */
    {   -1722,   17280 }, /* 142: -0.4922° */
    {   -1839,   17344 }, /* 143: -0.5273° */
    {   -1954,   17408 }, /* 144: -0.5625° */
    {   -2069,   17472 }, /* 145: -0.5977° */
    {   -2183,   17537 }, /* 146: -0.6328° */
    {   -2296,   17601 }, /* 147: -0.6680° */
    {   -2408,   17665 }, /* 148: -0.7031° */
    {   -2520,   17729 }, /* 149: -0.7383° */
    {   -2631,   17793 }, /* 150: -0.7734° */
    {   -2741,   17857 }, /* 151: -0.8086° */
    {   -2850,   17921 }, /* 152: -0.8438° */
    {   -2958,   17986 }, /* 153: -0.8789° */
    {   -3066,   18050 }, /* 154: -0.9141° */
    {   -3173,   18114 }, /* 155: -0.9492° */
    {   -3279,   18178 }, /* 156: -0.9844° */
    {   -3385,   18242 }, /* 157: -1.0195° */
    {   -3490,   18307 }, /* 158: -1.0547° */
    {   -3594,   18371 }, /* 159: -1.0898° */
    {   -3697,   18435 }, /* 160: -1.1250° */
    {   -3800,   18499 }, /* 161: -1.1602° */
    {   -3902,   18564 }, /* 162: -1.1953° */
    {   -4004,   18628 }, /* 163: -1.2305° */
    {   -4104,   18692 }, /* 164: -1.2656° */
    {   -4204,   18756 }, /* 165: -1.3008° */
    {   -4304,   18821 }, /* 166: -1.3359° */
    {   -4402,   18885 }, /* 167: -1.3711° */
    {   -4501,   18949 }, /* 168: -1.4063° */
    {   -4598,   19014 }, /* 169: -1.4414° */
    {   -4695,   19078 }, /* 170: -1.4766° */
    {   -4791,   19142 }, /* 171: -1.5117° */
    {   -4887,   19207 }, /* 172: -1.5469° */
    {   -4982,   19271 }, /* 173: -1.5820° */
    {   -5076,   19335 }, /* 174: -1.6172° */
    {   -5170,   19400 }, /* 175: -1.6523° */
    {   -5263,   19464 }, /* 176: -1.6875° */
    {   -5355,   19529 }, /* 177: -1.7227° */
    {   -5447,   19593 }, /* 178: -1.7578° */
    {   -5539,   19657 }, /* 179: -1.7930° */
    {   -5630,   19722 }, /* 180: -1.8281° */
    {   -5720,   19786 }, /* 181: -1.8633° */
    {   -5810,   19851 }, /* 182: -1.8984° */
    {   -5899,   19915 }, /* 183: -1.9336° */
    {   -5987,   19980 }, /* 184: -1.9688° */
    {   -6075,   20044 }, /* 185: -2.0039° */
    {   -6163,   20109 }, /* 186: -2.0391° */
    {   -6250,   20173 }, /* 187: -2.0742° */
    {   -6336,   20238 }, /* 188: -2.1094° */
    {   -6422,   20302 }, /* 189: -2.1445° */
    {   -6508,   20367 }, /* 190: -2.1797° */
    {   -6593,   20431 }, /* 191: -2.2148° */
    {   -6677,   20496 }, /* 192: -2.2500° */
    {   -6761,   20560 }, /* 193: -2.2852° */
    {   -6844,   20625 }, /* 194: -2.3203° */
    {   -6927,   20690 }, /* 195: -2.3555° */
    {   -7010,   20754 }, /* 196: -2.3906° */
    {   -7091,   20819 }, /* 197: -2.4258° */
    {   -7173,   20883 }, /* 198: -2.4609° */
    {   -7254,   20948 }, /* 199: -2.4961° */
    {   -7334,   21013 }, /* 200: -2.5313° */
    {   -7414,   21077 }, /* 201: -2.5664° */
    {   -7494,   21142 }, /* 202: -2.6016° */
    {   -7573,   21207 }, /* 203: -2.6367° */
    {   -7652,   21271 }, /* 204: -2.6719° */
    {   -7730,   21336 }, /* 205: -2.7070° */
    {   -7808,   21401 }, /* 206: -2.7422° */
    {   -7885,   21466 }, /* 207: -2.7773° */
    {   -7962,   21530 }, /* 208: -2.8125° */
    {   -8038,   21595 }, /* 209: -2.8477° */
    {   -8114,   21660 }, /* 210: -2.8828° */
    {   -8190,   21725 }, /* 211: -2.9180° */
    {   -8265,   21790 }, /* 212: -2.9531° */
    {   -8340,   21854 }, /* 213: -2.9883° */
    {   -8414,   21919 }, /* 214: -3.0234° */
    {   -8488,   21984 }, /* 215: -3.0586° */
    {   -8561,   22049 }, /* 216: -3.0938° */
    {   -8634,   22114 }, /* 217: -3.1289° */
    {   -8707,   22179 }, /* 218: -3.1641° */
    {   -8779,   22244 }, /* 219: -3.1992° */
    {   -8851,   22308 }, /* 220: -3.2344° */
    {   -8923,   22373 }, /* 221: -3.2695° */
    {   -8994,   22438 }, /* 222: -3.3047° */
    {   -9064,   22503 }, /* 223: -3.3398° */
    {   -9135,   22568 }, /* 224: -3.3750° */
    {   -9205,   22633 }, /* 225: -3.4102° */
    {   -9274,   22698 }, /* 226: -3.4453° */
    {   -9343,   22763 }, /* 227: -3.4805° */
    {   -9412,   22828 }, /* 228: -3.5156° */
    {   -9481,   22893 }, /* 229: -3.5508° */
    {   -9549,   22958 }, /* 230: -3.5859° */
    {   -9617,   23023 }, /* 231: -3.6211° */
    {   -9684,   23089 }, /* 232: -3.6563° */
    {   -9751,   23154 }, /* 233: -3.6914° */
    {   -9818,   23219 }, /* 234: -3.7266° */
    {   -9884,   23284 }, /* 235: -3.7617° */
    {   -9950,   23349 }, /* 236: -3.7969° */
    {  -10016,   23414 }, /* 237: -3.8320° */
    {  -10081,   23479 }, /* 238: -3.8672° */
    {  -10146,   23545 }, /* 239: -3.9023° */
    {  -10211,   23610 }, /* 240: -3.9375° */
    {  -10275,   23675 }, /* 241: -3.9727° */
    {  -10339,   23740 }, /* 242: -4.0078° */
    {  -10403,   23806 }, /* 243: -4.0430° */
    {  -10466,   23871 }, /* 244: -4.0781° */
    {  -10529,   23936 }, /* 245: -4.1133° */
    {  -10592,   24001 }, /* 246: -4.1484° */
    {  -10654,   24067 }, /* 247: -4.1836° */
    {  -10716,   24132 }, /* 248: -4.2188° */
    {  -10778,   24197 }, /* 249: -4.2539° */
    {  -10839,   24263 }, /* 250: -4.2891° */
    {  -10900,   24328 }, /* 251: -4.3242° */
    {  -10961,   24394 }, /* 252: -4.3594° */
    {  -11022,   24459 }, /* 253: -4.3945° */
    {  -11082,   24525 }, /* 254: -4.4297° */
    {  -11142,   24590 }  /* 255: -4.4648 deg */
};



#elif INT_SAMPLES_PER_10_SECONDS == 20000 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 2000 times per second. There are 256 steps in this table. 
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8228 }, /*   0:  5.4000° */
    {   32264,    8292 }, /*   1:  5.3578° */
    {   31768,    8355 }, /*   2:  5.3156° */
    {   31280,    8419 }, /*   3:  5.2734° */
    {   30799,    8483 }, /*   4:  5.2312° */
    {   30325,    8547 }, /*   5:  5.1891° */
    {   29858,    8610 }, /*   6:  5.1469° */
    {   29398,    8674 }, /*   7:  5.1047° */
    {   28944,    8738 }, /*   8:  5.0625° */
    {   28498,    8801 }, /*   9:  5.0203° */
    {   28057,    8865 }, /*  10:  4.9781° */
    {   27623,    8929 }, /*  11:  4.9359° */
    {   27195,    8992 }, /*  12:  4.8937° */
    {   26773,    9056 }, /*  13:  4.8516° */
    {   26357,    9120 }, /*  14:  4.8094° */
    {   25947,    9183 }, /*  15:  4.7672° */
    {   25542,    9247 }, /*  16:  4.7250° */
    {   25143,    9311 }, /*  17:  4.6828° */
    {   24749,    9374 }, /*  18:  4.6406° */
    {   24361,    9438 }, /*  19:  4.5984° */
    {   23978,    9502 }, /*  20:  4.5562° */
    {   23599,    9565 }, /*  21:  4.5141° */
    {   23226,    9629 }, /*  22:  4.4719° */
    {   22858,    9693 }, /*  23:  4.4297° */
    {   22494,    9756 }, /*  24:  4.3875° */
    {   22135,    9820 }, /*  25:  4.3453° */
    {   21781,    9883 }, /*  26:  4.3031° */
    {   21432,    9947 }, /*  27:  4.2609° */
    {   21086,   10011 }, /*  28:  4.2187° */
    {   20745,   10074 }, /*  29:  4.1766° */
    {   20409,   10138 }, /*  30:  4.1344° */
    {   20076,   10202 }, /*  31:  4.0922° */
    {   19748,   10265 }, /*  32:  4.0500° */
    {   19423,   10329 }, /*  33:  4.0078° */
    {   19103,   10392 }, /*  34:  3.9656° */
    {   18786,   10456 }, /*  35:  3.9234° */
    {   18473,   10520 }, /*  36:  3.8812° */
    {   18164,   10583 }, /*  37:  3.8391° */
    {   17859,   10647 }, /*  38:  3.7969° */
    {   17557,   10711 }, /*  39:  3.7547° */
    {   17259,   10774 }, /*  40:  3.7125° */
    {   16964,   10838 }, /*  41:  3.6703° */
    {   16673,   10901 }, /*  42:  3.6281° */
    {   16385,   10965 }, /*  43:  3.5859° */
    {   16100,   11029 }, /*  44:  3.5437° */
    {   15819,   11092 }, /*  45:  3.5016° */
    {   15541,   11156 }, /*  46:  3.4594° */
    {   15265,   11220 }, /*  47:  3.4172° */
    {   14993,   11283 }, /*  48:  3.3750° */
    {   14724,   11347 }, /*  49:  3.3328° */
    {   14458,   11410 }, /*  50:  3.2906° */
    {   14195,   11474 }, /*  51:  3.2484° */
    {   13935,   11538 }, /*  52:  3.2062° */
    {   13677,   11601 }, /*  53:  3.1641° */
    {   13423,   11665 }, /*  54:  3.1219° */
    {   13171,   11729 }, /*  55:  3.0797° */
    {   12922,   11792 }, /*  56:  3.0375° */
    {   12675,   11856 }, /*  57:  2.9953° */
    {   12431,   11919 }, /*  58:  2.9531° */
    {   12190,   11983 }, /*  59:  2.9109° */
    {   11951,   12047 }, /*  60:  2.8687° */
    {   11714,   12110 }, /*  61:  2.8266° */
    {   11480,   12174 }, /*  62:  2.7844° */
    {   11249,   12238 }, /*  63:  2.7422° */
    {   11020,   12301 }, /*  64:  2.7000° */
    {   10793,   12365 }, /*  65:  2.6578° */
    {   10568,   12429 }, /*  66:  2.6156° */
    {   10346,   12492 }, /*  67:  2.5734° */
    {   10126,   12556 }, /*  68:  2.5312° */
    {    9908,   12620 }, /*  69:  2.4891° */
    {    9692,   12683 }, /*  70:  2.4469° */
    {    9478,   12747 }, /*  71:  2.4047° */
    {    9267,   12811 }, /*  72:  2.3625° */
    {    9057,   12874 }, /*  73:  2.3203° */
    {    8850,   12938 }, /*  74:  2.2781° */
    {    8644,   13002 }, /*  75:  2.2359° */
    {    8441,   13065 }, /*  76:  2.1937° */
    {    8239,   13129 }, /*  77:  2.1516° */
    {    8039,   13193 }, /*  78:  2.1094° */
    {    7842,   13256 }, /*  79:  2.0672° */
    {    7646,   13320 }, /*  80:  2.0250° */
    {    7452,   13384 }, /*  81:  1.9828° */
    {    7259,   13447 }, /*  82:  1.9406° */
    {    7069,   13511 }, /*  83:  1.8984° */
    {    6880,   13575 }, /*  84:  1.8562° */
    {    6693,   13638 }, /*  85:  1.8141° */
    {    6508,   13702 }, /*  86:  1.7719° */
    {    6324,   13766 }, /*  87:  1.7297° */
    {    6142,   13830 }, /*  88:  1.6875° */
    {    5962,   13893 }, /*  89:  1.6453° */
    {    5783,   13957 }, /*  90:  1.6031° */
    {    5606,   14021 }, /*  91:  1.5609° */
    {    5431,   14085 }, /*  92:  1.5187° */
    {    5257,   14148 }, /*  93:  1.4766° */
    {    5084,   14212 }, /*  94:  1.4344° */
    {    4913,   14276 }, /*  95:  1.3922° */
    {    4744,   14340 }, /*  96:  1.3500° */
    {    4576,   14403 }, /*  97:  1.3078° */
    {    4409,   14467 }, /*  98:  1.2656° */
    {    4244,   14531 }, /*  99:  1.2234° */
    {    4080,   14595 }, /* 100:  1.1812° */
    {    3918,   14658 }, /* 101:  1.1391° */
    {    3757,   14722 }, /* 102:  1.0969° */
    {    3597,   14786 }, /* 103:  1.0547° */
    {    3439,   14850 }, /* 104:  1.0125° */
    {    3282,   14914 }, /* 105:  0.9703° */
    {    3126,   14978 }, /* 106:  0.9281° */
    {    2972,   15041 }, /* 107:  0.8859° */
    {    2819,   15105 }, /* 108:  0.8437° */
    {    2667,   15169 }, /* 109:  0.8016° */
    {    2516,   15233 }, /* 110:  0.7594° */
    {    2367,   15297 }, /* 111:  0.7172° */
    {    2218,   15361 }, /* 112:  0.6750° */
    {    2071,   15424 }, /* 113:  0.6328° */
    {    1926,   15488 }, /* 114:  0.5906° */
    {    1781,   15552 }, /* 115:  0.5484° */
    {    1637,   15616 }, /* 116:  0.5062° */
    {    1495,   15680 }, /* 117:  0.4641° */
    {    1354,   15744 }, /* 118:  0.4219° */
    {    1213,   15808 }, /* 119:  0.3797° */
    {    1074,   15872 }, /* 120:  0.3375° */
    {     936,   15936 }, /* 121:  0.2953° */
    {     799,   16000 }, /* 122:  0.2531° */
    {     664,   16064 }, /* 123:  0.2109° */
    {     529,   16128 }, /* 124:  0.1687° */
    {     395,   16192 }, /* 125:  0.1266° */
    {     262,   16256 }, /* 126:  0.0844° */
    {     130,   16320 }, /* 127:  0.0422° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -129,   16448 }, /* 129: -0.0422° */
    {    -258,   16512 }, /* 130: -0.0844° */
    {    -386,   16576 }, /* 131: -0.1266° */
    {    -513,   16640 }, /* 132: -0.1688° */
    {    -639,   16704 }, /* 133: -0.2109° */
    {    -764,   16768 }, /* 134: -0.2531° */
    {    -888,   16832 }, /* 135: -0.2953° */
    {   -1012,   16896 }, /* 136: -0.3375° */
    {   -1134,   16960 }, /* 137: -0.3797° */
    {   -1255,   17024 }, /* 138: -0.4219° */
    {   -1376,   17088 }, /* 139: -0.4641° */
    {   -1496,   17152 }, /* 140: -0.5063° */
    {   -1615,   17216 }, /* 141: -0.5484° */
    {   -1733,   17280 }, /* 142: -0.5906° */
    {   -1850,   17345 }, /* 143: -0.6328° */
    {   -1966,   17409 }, /* 144: -0.6750° */
    {   -2082,   17473 }, /* 145: -0.7172° */
    {   -2197,   17537 }, /* 146: -0.7594° */
    {   -2311,   17601 }, /* 147: -0.8016° */
    {   -2424,   17665 }, /* 148: -0.8438° */
    {   -2536,   17730 }, /* 149: -0.8859° */
    {   -2648,   17794 }, /* 150: -0.9281° */
    {   -2759,   17858 }, /* 151: -0.9703° */
    {   -2869,   17922 }, /* 152: -1.0125° */
    {   -2978,   17987 }, /* 153: -1.0547° */
    {   -3087,   18051 }, /* 154: -1.0969° */
    {   -3194,   18115 }, /* 155: -1.1391° */
    {   -3302,   18179 }, /* 156: -1.1813° */
    {   -3408,   18244 }, /* 157: -1.2234° */
    {   -3514,   18308 }, /* 158: -1.2656° */
    {   -3619,   18372 }, /* 159: -1.3078° */
    {   -3723,   18437 }, /* 160: -1.3500° */
    {   -3827,   18501 }, /* 161: -1.3922° */
    {   -3930,   18566 }, /* 162: -1.4344° */
    {   -4032,   18630 }, /* 163: -1.4766° */
    {   -4134,   18694 }, /* 164: -1.5188° */
    {   -4234,   18759 }, /* 165: -1.5609° */
    {   -4335,   18823 }, /* 166: -1.6031° */
    {   -4434,   18888 }, /* 167: -1.6453° */
    {   -4533,   18952 }, /* 168: -1.6875° */
    {   -4632,   19016 }, /* 169: -1.7297° */
    {   -4730,   19081 }, /* 170: -1.7719° */
    {   -4827,   19145 }, /* 171: -1.8141° */
    {   -4923,   19210 }, /* 172: -1.8563° */
    {   -5019,   19274 }, /* 173: -1.8984° */
    {   -5114,   19339 }, /* 174: -1.9406° */
    {   -5209,   19404 }, /* 175: -1.9828° */
    {   -5303,   19468 }, /* 176: -2.0250° */
    {   -5397,   19533 }, /* 177: -2.0672° */
    {   -5490,   19597 }, /* 178: -2.1094° */
    {   -5582,   19662 }, /* 179: -2.1516° */
    {   -5674,   19727 }, /* 180: -2.1938° */
    {   -5765,   19791 }, /* 181: -2.2359° */
    {   -5856,   19856 }, /* 182: -2.2781° */
    {   -5946,   19921 }, /* 183: -2.3203° */
    {   -6036,   19985 }, /* 184: -2.3625° */
    {   -6125,   20050 }, /* 185: -2.4047° */
    {   -6213,   20115 }, /* 186: -2.4469° */
    {   -6301,   20179 }, /* 187: -2.4891° */
    {   -6389,   20244 }, /* 188: -2.5313° */
    {   -6475,   20309 }, /* 189: -2.5734° */
    {   -6562,   20374 }, /* 190: -2.6156° */
    {   -6648,   20439 }, /* 191: -2.6578° */
    {   -6733,   20503 }, /* 192: -2.7000° */
    {   -6818,   20568 }, /* 193: -2.7422° */
    {   -6903,   20633 }, /* 194: -2.7844° */
    {   -6987,   20698 }, /* 195: -2.8266° */
    {   -7070,   20763 }, /* 196: -2.8688° */
    {   -7153,   20828 }, /* 197: -2.9109° */
    {   -7235,   20893 }, /* 198: -2.9531° */
    {   -7317,   20958 }, /* 199: -2.9953° */
    {   -7399,   21023 }, /* 200: -3.0375° */
    {   -7480,   21087 }, /* 201: -3.0797° */
    {   -7561,   21152 }, /* 202: -3.1219° */
    {   -7641,   21218 }, /* 203: -3.1641° */
    {   -7720,   21283 }, /* 204: -3.2063° */
    {   -7800,   21348 }, /* 205: -3.2484° */
    {   -7879,   21413 }, /* 206: -3.2906° */
    {   -7957,   21478 }, /* 207: -3.3328° */
    {   -8035,   21543 }, /* 208: -3.3750° */
    {   -8112,   21608 }, /* 209: -3.4172° */
    {   -8189,   21673 }, /* 210: -3.4594° */
    {   -8266,   21738 }, /* 211: -3.5016° */
    {   -8342,   21803 }, /* 212: -3.5438° */
    {   -8418,   21869 }, /* 213: -3.5859° */
    {   -8494,   21934 }, /* 214: -3.6281° */
    {   -8569,   21999 }, /* 215: -3.6703° */
    {   -8643,   22064 }, /* 216: -3.7125° */
    {   -8717,   22130 }, /* 217: -3.7547° */
    {   -8791,   22195 }, /* 218: -3.7969° */
    {   -8865,   22260 }, /* 219: -3.8391° */
    {   -8938,   22326 }, /* 220: -3.8813° */
    {   -9010,   22391 }, /* 221: -3.9234° */
    {   -9082,   22456 }, /* 222: -3.9656° */
    {   -9154,   22522 }, /* 223: -4.0078° */
    {   -9226,   22587 }, /* 224: -4.0500° */
    {   -9297,   22653 }, /* 225: -4.0922° */
    {   -9368,   22718 }, /* 226: -4.1344° */
    {   -9438,   22784 }, /* 227: -4.1766° */
    {   -9508,   22849 }, /* 228: -4.2188° */
    {   -9578,   22915 }, /* 229: -4.2609° */
    {   -9647,   22980 }, /* 230: -4.3031° */
    {   -9716,   23046 }, /* 231: -4.3453° */
    {   -9784,   23112 }, /* 232: -4.3875° */
    {   -9853,   23177 }, /* 233: -4.4297° */
    {   -9920,   23243 }, /* 234: -4.4719° */
    {   -9988,   23309 }, /* 235: -4.5141° */
    {  -10055,   23374 }, /* 236: -4.5563° */
    {  -10122,   23440 }, /* 237: -4.5984° */
    {  -10188,   23506 }, /* 238: -4.6406° */
    {  -10255,   23572 }, /* 239: -4.6828° */
    {  -10320,   23637 }, /* 240: -4.7250° */
    {  -10386,   23703 }, /* 241: -4.7672° */
    {  -10451,   23769 }, /* 242: -4.8094° */
    {  -10516,   23835 }, /* 243: -4.8516° */
    {  -10581,   23901 }, /* 244: -4.8938° */
    {  -10645,   23967 }, /* 245: -4.9359° */
    {  -10709,   24033 }, /* 246: -4.9781° */
    {  -10772,   24099 }, /* 247: -5.0203° */
    {  -10836,   24165 }, /* 248: -5.0625° */
    {  -10899,   24231 }, /* 249: -5.1047° */
    {  -10961,   24297 }, /* 250: -5.1469° */
    {  -11024,   24363 }, /* 251: -5.1891° */
    {  -11086,   24429 }, /* 252: -5.2313° */
    {  -11148,   24495 }, /* 253: -5.2734° */
    {  -11209,   24561 }, /* 254: -5.3156° */
    {  -11270,   24627 }  /* 255: -5.3578 deg */
};

#elif INT_SAMPLES_PER_10_SECONDS == 29297  && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 2929 times per second. There are 256 steps in this table. 
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8203 }, /*   0:  3.0720° */
    {   32261,    8267 }, /*   1:  3.0480° */
    {   31762,    8331 }, /*   2:  3.0240° */
    {   31271,    8395 }, /*   3:  3.0000° */
    {   30787,    8459 }, /*   4:  2.9760° */
    {   30311,    8523 }, /*   5:  2.9520° */
    {   29841,    8587 }, /*   6:  2.9280° */
    {   29379,    8651 }, /*   7:  2.9040° */
    {   28923,    8715 }, /*   8:  2.8800° */
    {   28474,    8778 }, /*   9:  2.8560° */
    {   28031,    8842 }, /*  10:  2.8320° */
    {   27595,    8906 }, /*  11:  2.8080° */
    {   27165,    8970 }, /*  12:  2.7840° */
    {   26741,    9034 }, /*  13:  2.7600° */
    {   26323,    9098 }, /*  14:  2.7360° */
    {   25911,    9162 }, /*  15:  2.7120° */
    {   25504,    9226 }, /*  16:  2.6880° */
    {   25103,    9290 }, /*  17:  2.6640° */
    {   24708,    9353 }, /*  18:  2.6400° */
    {   24318,    9417 }, /*  19:  2.6160° */
    {   23933,    9481 }, /*  20:  2.5920° */
    {   23553,    9545 }, /*  21:  2.5680° */
    {   23178,    9609 }, /*  22:  2.5440° */
    {   22809,    9673 }, /*  23:  2.5200° */
    {   22444,    9737 }, /*  24:  2.4960° */
    {   22084,    9801 }, /*  25:  2.4720° */
    {   21728,    9865 }, /*  26:  2.4480° */
    {   21377,    9928 }, /*  27:  2.4240° */
    {   21031,    9992 }, /*  28:  2.4000° */
    {   20689,   10056 }, /*  29:  2.3760° */
    {   20351,   10120 }, /*  30:  2.3520° */
    {   20018,   10184 }, /*  31:  2.3280° */
    {   19689,   10248 }, /*  32:  2.3040° */
    {   19363,   10312 }, /*  33:  2.2800° */
    {   19042,   10376 }, /*  34:  2.2560° */
    {   18725,   10439 }, /*  35:  2.2320° */
    {   18411,   10503 }, /*  36:  2.2080° */
    {   18102,   10567 }, /*  37:  2.1840° */
    {   17796,   10631 }, /*  38:  2.1600° */
    {   17493,   10695 }, /*  39:  2.1360° */
    {   17195,   10759 }, /*  40:  2.1120° */
    {   16899,   10823 }, /*  41:  2.0880° */
    {   16608,   10887 }, /*  42:  2.0640° */
    {   16319,   10950 }, /*  43:  2.0400° */
    {   16034,   11014 }, /*  44:  2.0160° */
    {   15752,   11078 }, /*  45:  1.9920° */
    {   15474,   11142 }, /*  46:  1.9680° */
    {   15198,   11206 }, /*  47:  1.9440° */
    {   14926,   11270 }, /*  48:  1.9200° */
    {   14657,   11334 }, /*  49:  1.8960° */
    {   14391,   11398 }, /*  50:  1.8720° */
    {   14128,   11461 }, /*  51:  1.8480° */
    {   13867,   11525 }, /*  52:  1.8240° */
    {   13610,   11589 }, /*  53:  1.8000° */
    {   13355,   11653 }, /*  54:  1.7760° */
    {   13103,   11717 }, /*  55:  1.7520° */
    {   12854,   11781 }, /*  56:  1.7280° */
    {   12608,   11845 }, /*  57:  1.7040° */
    {   12364,   11909 }, /*  58:  1.6800° */
    {   12122,   11973 }, /*  59:  1.6560° */
    {   11884,   12036 }, /*  60:  1.6320° */
    {   11648,   12100 }, /*  61:  1.6080° */
    {   11414,   12164 }, /*  62:  1.5840° */
    {   11183,   12228 }, /*  63:  1.5600° */
    {   10954,   12292 }, /*  64:  1.5360° */
    {   10727,   12356 }, /*  65:  1.5120° */
    {   10503,   12420 }, /*  66:  1.4880° */
    {   10281,   12484 }, /*  67:  1.4640° */
    {   10061,   12547 }, /*  68:  1.4400° */
    {    9844,   12611 }, /*  69:  1.4160° */
    {    9628,   12675 }, /*  70:  1.3920° */
    {    9415,   12739 }, /*  71:  1.3680° */
    {    9204,   12803 }, /*  72:  1.3440° */
    {    8995,   12867 }, /*  73:  1.3200° */
    {    8788,   12931 }, /*  74:  1.2960° */
    {    8584,   12995 }, /*  75:  1.2720° */
    {    8381,   13059 }, /*  76:  1.2480° */
    {    8180,   13122 }, /*  77:  1.2240° */
    {    7981,   13186 }, /*  78:  1.2000° */
    {    7784,   13250 }, /*  79:  1.1760° */
    {    7589,   13314 }, /*  80:  1.1520° */
    {    7395,   13378 }, /*  81:  1.1280° */
    {    7204,   13442 }, /*  82:  1.1040° */
    {    7014,   13506 }, /*  83:  1.0800° */
    {    6826,   13570 }, /*  84:  1.0560° */
    {    6640,   13634 }, /*  85:  1.0320° */
    {    6456,   13698 }, /*  86:  1.0080° */
    {    6273,   13762 }, /*  87:  0.9840° */
    {    6092,   13825 }, /*  88:  0.9600° */
    {    5912,   13889 }, /*  89:  0.9360° */
    {    5735,   13953 }, /*  90:  0.9120° */
    {    5558,   14017 }, /*  91:  0.8880° */
    {    5384,   14081 }, /*  92:  0.8640° */
    {    5211,   14145 }, /*  93:  0.8400° */
    {    5039,   14209 }, /*  94:  0.8160° */
    {    4869,   14273 }, /*  95:  0.7920° */
    {    4701,   14337 }, /*  96:  0.7680° */
    {    4534,   14401 }, /*  97:  0.7440° */
    {    4368,   14465 }, /*  98:  0.7200° */
    {    4204,   14529 }, /*  99:  0.6960° */
    {    4042,   14593 }, /* 100:  0.6720° */
    {    3881,   14656 }, /* 101:  0.6480° */
    {    3721,   14720 }, /* 102:  0.6240° */
    {    3562,   14784 }, /* 103:  0.6000° */
    {    3405,   14848 }, /* 104:  0.5760° */
    {    3249,   14912 }, /* 105:  0.5520° */
    {    3095,   14976 }, /* 106:  0.5280° */
    {    2942,   15040 }, /* 107:  0.5040° */
    {    2790,   15104 }, /* 108:  0.4800° */
    {    2639,   15168 }, /* 109:  0.4560° */
    {    2490,   15232 }, /* 110:  0.4320° */
    {    2342,   15296 }, /* 111:  0.4080° */
    {    2195,   15360 }, /* 112:  0.3840° */
    {    2049,   15424 }, /* 113:  0.3600° */
    {    1905,   15488 }, /* 114:  0.3360° */
    {    1762,   15552 }, /* 115:  0.3120° */
    {    1619,   15616 }, /* 116:  0.2880° */
    {    1478,   15680 }, /* 117:  0.2640° */
    {    1339,   15744 }, /* 118:  0.2400° */
    {    1200,   15808 }, /* 119:  0.2160° */
    {    1062,   15872 }, /* 120:  0.1920° */
    {     926,   15936 }, /* 121:  0.1680° */
    {     790,   16000 }, /* 122:  0.1440° */
    {     656,   16064 }, /* 123:  0.1200° */
    {     523,   16128 }, /* 124:  0.0960° */
    {     390,   16192 }, /* 125:  0.0720° */
    {     259,   16255 }, /* 126:  0.0480° */
    {     129,   16319 }, /* 127:  0.0240° */
    {       0,   16383 }, /* 128: -0.0000° */
    {    -128,   16447 }, /* 129: -0.0240° */
    {    -255,   16511 }, /* 130: -0.0480° */
    {    -381,   16576 }, /* 131: -0.0720° */
    {    -507,   16640 }, /* 132: -0.0960° */
    {    -631,   16704 }, /* 133: -0.1200° */
    {    -754,   16768 }, /* 134: -0.1440° */
    {    -877,   16832 }, /* 135: -0.1680° */
    {    -999,   16896 }, /* 136: -0.1920° */
    {   -1119,   16960 }, /* 137: -0.2160° */
    {   -1239,   17024 }, /* 138: -0.2400° */
    {   -1358,   17088 }, /* 139: -0.2640° */
    {   -1476,   17152 }, /* 140: -0.2880° */
    {   -1593,   17216 }, /* 141: -0.3120° */
    {   -1709,   17280 }, /* 142: -0.3360° */
    {   -1825,   17344 }, /* 143: -0.3600° */
    {   -1940,   17408 }, /* 144: -0.3840° */
    {   -2053,   17472 }, /* 145: -0.4080° */
    {   -2166,   17536 }, /* 146: -0.4320° */
    {   -2279,   17600 }, /* 147: -0.4560° */
    {   -2390,   17664 }, /* 148: -0.4800° */
    {   -2500,   17728 }, /* 149: -0.5040° */
    {   -2610,   17792 }, /* 150: -0.5280° */
    {   -2719,   17856 }, /* 151: -0.5520° */
    {   -2827,   17920 }, /* 152: -0.5760° */
    {   -2935,   17984 }, /* 153: -0.6000° */
    {   -3042,   18049 }, /* 154: -0.6240° */
    {   -3148,   18113 }, /* 155: -0.6480° */
    {   -3253,   18177 }, /* 156: -0.6720° */
    {   -3357,   18241 }, /* 157: -0.6960° */
    {   -3461,   18305 }, /* 158: -0.7200° */
    {   -3564,   18369 }, /* 159: -0.7440° */
    {   -3667,   18433 }, /* 160: -0.7680° */
    {   -3768,   18497 }, /* 161: -0.7920° */
    {   -3869,   18561 }, /* 162: -0.8160° */
    {   -3970,   18626 }, /* 163: -0.8400° */
    {   -4069,   18690 }, /* 164: -0.8640° */
    {   -4168,   18754 }, /* 165: -0.8880° */
    {   -4267,   18818 }, /* 166: -0.9120° */
    {   -4364,   18882 }, /* 167: -0.9360° */
    {   -4461,   18946 }, /* 168: -0.9600° */
    {   -4558,   19010 }, /* 169: -0.9840° */
    {   -4653,   19074 }, /* 170: -1.0080° */
    {   -4748,   19139 }, /* 171: -1.0320° */
    {   -4843,   19203 }, /* 172: -1.0560° */
    {   -4937,   19267 }, /* 173: -1.0800° */
    {   -5030,   19331 }, /* 174: -1.1040° */
    {   -5123,   19395 }, /* 175: -1.1280° */
    {   -5215,   19459 }, /* 176: -1.1520° */
    {   -5306,   19524 }, /* 177: -1.1760° */
    {   -5397,   19588 }, /* 178: -1.2000° */
    {   -5487,   19652 }, /* 179: -1.2240° */
    {   -5577,   19716 }, /* 180: -1.2480° */
    {   -5666,   19780 }, /* 181: -1.2720° */
    {   -5755,   19845 }, /* 182: -1.2960° */
    {   -5843,   19909 }, /* 183: -1.3200° */
    {   -5930,   19973 }, /* 184: -1.3440° */
    {   -6017,   20037 }, /* 185: -1.3680° */
    {   -6103,   20102 }, /* 186: -1.3920° */
    {   -6189,   20166 }, /* 187: -1.4160° */
    {   -6274,   20230 }, /* 188: -1.4400° */
    {   -6359,   20294 }, /* 189: -1.4640° */
    {   -6443,   20358 }, /* 190: -1.4880° */
    {   -6527,   20423 }, /* 191: -1.5120° */
    {   -6610,   20487 }, /* 192: -1.5360° */
    {   -6693,   20551 }, /* 193: -1.5600° */
    {   -6775,   20615 }, /* 194: -1.5840° */
    {   -6857,   20680 }, /* 195: -1.6080° */
    {   -6938,   20744 }, /* 196: -1.6320° */
    {   -7019,   20808 }, /* 197: -1.6560° */
    {   -7099,   20873 }, /* 198: -1.6800° */
    {   -7179,   20937 }, /* 199: -1.7040° */
    {   -7258,   21001 }, /* 200: -1.7280° */
    {   -7337,   21066 }, /* 201: -1.7520° */
    {   -7415,   21130 }, /* 202: -1.7760° */
    {   -7493,   21194 }, /* 203: -1.8000° */
    {   -7570,   21258 }, /* 204: -1.8240° */
    {   -7647,   21323 }, /* 205: -1.8480° */
    {   -7724,   21387 }, /* 206: -1.8720° */
    {   -7800,   21451 }, /* 207: -1.8960° */
    {   -7875,   21516 }, /* 208: -1.9200° */
    {   -7950,   21580 }, /* 209: -1.9440° */
    {   -8025,   21644 }, /* 210: -1.9680° */
    {   -8099,   21709 }, /* 211: -1.9920° */
    {   -8173,   21773 }, /* 212: -2.0160° */
    {   -8247,   21838 }, /* 213: -2.0400° */
    {   -8320,   21902 }, /* 214: -2.0640° */
    {   -8392,   21966 }, /* 215: -2.0880° */
    {   -8464,   22031 }, /* 216: -2.1120° */
    {   -8536,   22095 }, /* 217: -2.1360° */
    {   -8608,   22160 }, /* 218: -2.1600° */
    {   -8678,   22224 }, /* 219: -2.1840° */
    {   -8749,   22288 }, /* 220: -2.2080° */
    {   -8819,   22353 }, /* 221: -2.2320° */
    {   -8889,   22417 }, /* 222: -2.2560° */
    {   -8958,   22482 }, /* 223: -2.2800° */
    {   -9027,   22546 }, /* 224: -2.3040° */
    {   -9096,   22611 }, /* 225: -2.3280° */
    {   -9164,   22675 }, /* 226: -2.3520° */
    {   -9232,   22739 }, /* 227: -2.3760° */
    {   -9300,   22804 }, /* 228: -2.4000° */
    {   -9367,   22868 }, /* 229: -2.4240° */
    {   -9433,   22933 }, /* 230: -2.4480° */
    {   -9500,   22997 }, /* 231: -2.4720° */
    {   -9566,   23062 }, /* 232: -2.4960° */
    {   -9632,   23126 }, /* 233: -2.5200° */
    {   -9697,   23191 }, /* 234: -2.5440° */
    {   -9762,   23255 }, /* 235: -2.5680° */
    {   -9826,   23320 }, /* 236: -2.5920° */
    {   -9891,   23384 }, /* 237: -2.6160° */
    {   -9955,   23449 }, /* 238: -2.6400° */
    {  -10018,   23513 }, /* 239: -2.6640° */
    {  -10082,   23578 }, /* 240: -2.6880° */
    {  -10144,   23643 }, /* 241: -2.7120° */
    {  -10207,   23707 }, /* 242: -2.7360° */
    {  -10269,   23772 }, /* 243: -2.7600° */
    {  -10331,   23836 }, /* 244: -2.7840° */
    {  -10393,   23901 }, /* 245: -2.8080° */
    {  -10454,   23965 }, /* 246: -2.8320° */
    {  -10515,   24030 }, /* 247: -2.8560° */
    {  -10576,   24095 }, /* 248: -2.8800° */
    {  -10636,   24159 }, /* 249: -2.9040° */
    {  -10696,   24224 }, /* 250: -2.9280° */
    {  -10756,   24288 }, /* 251: -2.9520° */
    {  -10815,   24353 }, /* 252: -2.9760° */
    {  -10874,   24418 }, /* 253: -3.0000° */
    {  -10933,   24482 }, /* 254: -3.0240° */
    {  -10992,   24547 }  /* 255: -3.0480 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 29297 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 2929 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8208 }, /*   0:  3.6864° */
    {   32262,    8272 }, /*   1:  3.6576° */
    {   31763,    8336 }, /*   2:  3.6288° */
    {   31273,    8400 }, /*   3:  3.6000° */
    {   30790,    8464 }, /*   4:  3.5712° */
    {   30314,    8528 }, /*   5:  3.5424° */
    {   29845,    8592 }, /*   6:  3.5136° */
    {   29383,    8656 }, /*   7:  3.4848° */
    {   28927,    8719 }, /*   8:  3.4560° */
    {   28479,    8783 }, /*   9:  3.4272° */
    {   28037,    8847 }, /*  10:  3.3984° */
    {   27601,    8911 }, /*  11:  3.3696° */
    {   27171,    8975 }, /*  12:  3.3408° */
    {   26748,    9039 }, /*  13:  3.3120° */
    {   26330,    9102 }, /*  14:  3.2832° */
    {   25918,    9166 }, /*  15:  3.2544° */
    {   25512,    9230 }, /*  16:  3.2256° */
    {   25112,    9294 }, /*  17:  3.1968° */
    {   24716,    9358 }, /*  18:  3.1680° */
    {   24327,    9422 }, /*  19:  3.1392° */
    {   23942,    9485 }, /*  20:  3.1104° */
    {   23563,    9549 }, /*  21:  3.0816° */
    {   23189,    9613 }, /*  22:  3.0528° */
    {   22819,    9677 }, /*  23:  3.0240° */
    {   22455,    9741 }, /*  24:  2.9952° */
    {   22095,    9805 }, /*  25:  2.9664° */
    {   21739,    9868 }, /*  26:  2.9376° */
    {   21389,    9932 }, /*  27:  2.9088° */
    {   21043,    9996 }, /*  28:  2.8800° */
    {   20701,   10060 }, /*  29:  2.8512° */
    {   20363,   10124 }, /*  30:  2.8224° */
    {   20030,   10188 }, /*  31:  2.7936° */
    {   19701,   10251 }, /*  32:  2.7648° */
    {   19376,   10315 }, /*  33:  2.7360° */
    {   19055,   10379 }, /*  34:  2.7072° */
    {   18738,   10443 }, /*  35:  2.6784° */
    {   18424,   10507 }, /*  36:  2.6496° */
    {   18115,   10571 }, /*  37:  2.6208° */
    {   17809,   10634 }, /*  38:  2.5920° */
    {   17507,   10698 }, /*  39:  2.5632° */
    {   17208,   10762 }, /*  40:  2.5344° */
    {   16913,   10826 }, /*  41:  2.5056° */
    {   16621,   10890 }, /*  42:  2.4768° */
    {   16333,   10954 }, /*  43:  2.4480° */
    {   16048,   11017 }, /*  44:  2.4192° */
    {   15766,   11081 }, /*  45:  2.3904° */
    {   15488,   11145 }, /*  46:  2.3616° */
    {   15213,   11209 }, /*  47:  2.3328° */
    {   14940,   11273 }, /*  48:  2.3040° */
    {   14671,   11336 }, /*  49:  2.2752° */
    {   14405,   11400 }, /*  50:  2.2464° */
    {   14142,   11464 }, /*  51:  2.2176° */
    {   13881,   11528 }, /*  52:  2.1888° */
    {   13624,   11592 }, /*  53:  2.1600° */
    {   13369,   11656 }, /*  54:  2.1312° */
    {   13117,   11719 }, /*  55:  2.1024° */
    {   12868,   11783 }, /*  56:  2.0736° */
    {   12622,   11847 }, /*  57:  2.0448° */
    {   12378,   11911 }, /*  58:  2.0160° */
    {   12137,   11975 }, /*  59:  1.9872° */
    {   11898,   12039 }, /*  60:  1.9584° */
    {   11662,   12102 }, /*  61:  1.9296° */
    {   11428,   12166 }, /*  62:  1.9008° */
    {   11196,   12230 }, /*  63:  1.8720° */
    {   10967,   12294 }, /*  64:  1.8432° */
    {   10741,   12358 }, /*  65:  1.8144° */
    {   10517,   12422 }, /*  66:  1.7856° */
    {   10295,   12485 }, /*  67:  1.7568° */
    {   10075,   12549 }, /*  68:  1.7280° */
    {    9857,   12613 }, /*  69:  1.6992° */
    {    9642,   12677 }, /*  70:  1.6704° */
    {    9429,   12741 }, /*  71:  1.6416° */
    {    9217,   12805 }, /*  72:  1.6128° */
    {    9008,   12868 }, /*  73:  1.5840° */
    {    8801,   12932 }, /*  74:  1.5552° */
    {    8596,   12996 }, /*  75:  1.5264° */
    {    8393,   13060 }, /*  76:  1.4976° */
    {    8192,   13124 }, /*  77:  1.4688° */
    {    7993,   13188 }, /*  78:  1.4400° */
    {    7796,   13252 }, /*  79:  1.4112° */
    {    7601,   13315 }, /*  80:  1.3824° */
    {    7407,   13379 }, /*  81:  1.3536° */
    {    7215,   13443 }, /*  82:  1.3248° */
    {    7026,   13507 }, /*  83:  1.2960° */
    {    6837,   13571 }, /*  84:  1.2672° */
    {    6651,   13635 }, /*  85:  1.2384° */
    {    6467,   13699 }, /*  86:  1.2096° */
    {    6284,   13762 }, /*  87:  1.1808° */
    {    6102,   13826 }, /*  88:  1.1520° */
    {    5923,   13890 }, /*  89:  1.1232° */
    {    5745,   13954 }, /*  90:  1.0944° */
    {    5568,   14018 }, /*  91:  1.0656° */
    {    5394,   14082 }, /*  92:  1.0368° */
    {    5220,   14146 }, /*  93:  1.0080° */
    {    5049,   14210 }, /*  94:  0.9792° */
    {    4879,   14273 }, /*  95:  0.9504° */
    {    4710,   14337 }, /*  96:  0.9216° */
    {    4543,   14401 }, /*  97:  0.8928° */
    {    4377,   14465 }, /*  98:  0.8640° */
    {    4213,   14529 }, /*  99:  0.8352° */
    {    4050,   14593 }, /* 100:  0.8064° */
    {    3888,   14657 }, /* 101:  0.7776° */
    {    3728,   14721 }, /* 102:  0.7488° */
    {    3570,   14785 }, /* 103:  0.7200° */
    {    3412,   14849 }, /* 104:  0.6912° */
    {    3256,   14913 }, /* 105:  0.6624° */
    {    3101,   14976 }, /* 106:  0.6336° */
    {    2948,   15040 }, /* 107:  0.6048° */
    {    2796,   15104 }, /* 108:  0.5760° */
    {    2645,   15168 }, /* 109:  0.5472° */
    {    2495,   15232 }, /* 110:  0.5184° */
    {    2347,   15296 }, /* 111:  0.4896° */
    {    2200,   15360 }, /* 112:  0.4608° */
    {    2054,   15424 }, /* 113:  0.4320° */
    {    1909,   15488 }, /* 114:  0.4032° */
    {    1766,   15552 }, /* 115:  0.3744° */
    {    1623,   15616 }, /* 116:  0.3456° */
    {    1482,   15680 }, /* 117:  0.3168° */
    {    1342,   15744 }, /* 118:  0.2880° */
    {    1203,   15808 }, /* 119:  0.2592° */
    {    1065,   15872 }, /* 120:  0.2304° */
    {     928,   15936 }, /* 121:  0.2016° */
    {     792,   16000 }, /* 122:  0.1728° */
    {     657,   16064 }, /* 123:  0.1440° */
    {     524,   16128 }, /* 124:  0.1152° */
    {     391,   16192 }, /* 125:  0.0864° */
    {     260,   16256 }, /* 126:  0.0576° */
    {     129,   16320 }, /* 127:  0.0288° */
    {       0,   16383 }, /* 128:  0.0000° */
    {    -128,   16448 }, /* 129: -0.0288° */
    {    -256,   16512 }, /* 130: -0.0576° */
    {    -382,   16576 }, /* 131: -0.0864° */
    {    -508,   16640 }, /* 132: -0.1152° */
    {    -633,   16704 }, /* 133: -0.1440° */
    {    -756,   16768 }, /* 134: -0.1728° */
    {    -879,   16832 }, /* 135: -0.2016° */
    {   -1001,   16896 }, /* 136: -0.2304° */
    {   -1122,   16960 }, /* 137: -0.2592° */
    {   -1242,   17024 }, /* 138: -0.2880° */
    {   -1362,   17088 }, /* 139: -0.3168° */
    {   -1480,   17152 }, /* 140: -0.3456° */
    {   -1598,   17216 }, /* 141: -0.3744° */
    {   -1714,   17280 }, /* 142: -0.4032° */
    {   -1830,   17344 }, /* 143: -0.4320° */
    {   -1945,   17408 }, /* 144: -0.4608° */
    {   -2059,   17472 }, /* 145: -0.4896° */
    {   -2173,   17536 }, /* 146: -0.5184° */
    {   -2285,   17600 }, /* 147: -0.5472° */
    {   -2397,   17664 }, /* 148: -0.5760° */
    {   -2508,   17729 }, /* 149: -0.6048° */
    {   -2618,   17793 }, /* 150: -0.6336° */
    {   -2727,   17857 }, /* 151: -0.6624° */
    {   -2836,   17921 }, /* 152: -0.6912° */
    {   -2944,   17985 }, /* 153: -0.7200° */
    {   -3051,   18049 }, /* 154: -0.7488° */
    {   -3157,   18113 }, /* 155: -0.7776° */
    {   -3263,   18177 }, /* 156: -0.8064° */
    {   -3368,   18241 }, /* 157: -0.8352° */
    {   -3472,   18306 }, /* 158: -0.8640° */
    {   -3576,   18370 }, /* 159: -0.8928° */
    {   -3678,   18434 }, /* 160: -0.9216° */
    {   -3781,   18498 }, /* 161: -0.9504° */
    {   -3882,   18562 }, /* 162: -0.9792° */
    {   -3983,   18626 }, /* 163: -1.0080° */
    {   -4083,   18691 }, /* 164: -1.0368° */
    {   -4182,   18755 }, /* 165: -1.0656° */
    {   -4281,   18819 }, /* 166: -1.0944° */
    {   -4379,   18883 }, /* 167: -1.1232° */
    {   -4476,   18947 }, /* 168: -1.1520° */
    {   -4573,   19012 }, /* 169: -1.1808° */
    {   -4669,   19076 }, /* 170: -1.2096° */
    {   -4765,   19140 }, /* 171: -1.2384° */
    {   -4860,   19204 }, /* 172: -1.2672° */
    {   -4954,   19269 }, /* 173: -1.2960° */
    {   -5048,   19333 }, /* 174: -1.3248° */
    {   -5141,   19397 }, /* 175: -1.3536° */
    {   -5233,   19461 }, /* 176: -1.3824° */
    {   -5325,   19526 }, /* 177: -1.4112° */
    {   -5416,   19590 }, /* 178: -1.4400° */
    {   -5507,   19654 }, /* 179: -1.4688° */
    {   -5597,   19718 }, /* 180: -1.4976° */
    {   -5687,   19783 }, /* 181: -1.5264° */
    {   -5776,   19847 }, /* 182: -1.5552° */
    {   -5864,   19911 }, /* 183: -1.5840° */
    {   -5952,   19976 }, /* 184: -1.6128° */
    {   -6039,   20040 }, /* 185: -1.6416° */
    {   -6126,   20104 }, /* 186: -1.6704° */
    {   -6212,   20169 }, /* 187: -1.6992° */
    {   -6298,   20233 }, /* 188: -1.7280° */
    {   -6383,   20297 }, /* 189: -1.7568° */
    {   -6468,   20362 }, /* 190: -1.7856° */
    {   -6552,   20426 }, /* 191: -1.8144° */
    {   -6636,   20490 }, /* 192: -1.8432° */
    {   -6719,   20555 }, /* 193: -1.8720° */
    {   -6801,   20619 }, /* 194: -1.9008° */
    {   -6884,   20683 }, /* 195: -1.9296° */
    {   -6965,   20748 }, /* 196: -1.9584° */
    {   -7046,   20812 }, /* 197: -1.9872° */
    {   -7127,   20877 }, /* 198: -2.0160° */
    {   -7207,   20941 }, /* 199: -2.0448° */
    {   -7287,   21006 }, /* 200: -2.0736° */
    {   -7366,   21070 }, /* 201: -2.1024° */
    {   -7445,   21134 }, /* 202: -2.1312° */
    {   -7523,   21199 }, /* 203: -2.1600° */
    {   -7601,   21263 }, /* 204: -2.1888° */
    {   -7679,   21328 }, /* 205: -2.2176° */
    {   -7756,   21392 }, /* 206: -2.2464° */
    {   -7832,   21457 }, /* 207: -2.2752° */
    {   -7908,   21521 }, /* 208: -2.3040° */
    {   -7984,   21586 }, /* 209: -2.3328° */
    {   -8059,   21650 }, /* 210: -2.3616° */
    {   -8134,   21715 }, /* 211: -2.3904° */
    {   -8208,   21779 }, /* 212: -2.4192° */
    {   -8282,   21844 }, /* 213: -2.4480° */
    {   -8356,   21908 }, /* 214: -2.4768° */
    {   -8429,   21973 }, /* 215: -2.5056° */
    {   -8501,   22038 }, /* 216: -2.5344° */
    {   -8574,   22102 }, /* 217: -2.5632° */
    {   -8645,   22167 }, /* 218: -2.5920° */
    {   -8717,   22231 }, /* 219: -2.6208° */
    {   -8788,   22296 }, /* 220: -2.6496° */
    {   -8859,   22361 }, /* 221: -2.6784° */
    {   -8929,   22425 }, /* 222: -2.7072° */
    {   -8999,   22490 }, /* 223: -2.7360° */
    {   -9068,   22554 }, /* 224: -2.7648° */
    {   -9137,   22619 }, /* 225: -2.7936° */
    {   -9206,   22684 }, /* 226: -2.8224° */
    {   -9274,   22748 }, /* 227: -2.8512° */
    {   -9342,   22813 }, /* 228: -2.8800° */
    {   -9410,   22878 }, /* 229: -2.9088° */
    {   -9477,   22943 }, /* 230: -2.9376° */
    {   -9544,   23007 }, /* 231: -2.9664° */
    {   -9611,   23072 }, /* 232: -2.9952° */
    {   -9677,   23137 }, /* 233: -3.0240° */
    {   -9743,   23201 }, /* 234: -3.0528° */
    {   -9808,   23266 }, /* 235: -3.0816° */
    {   -9873,   23331 }, /* 236: -3.1104° */
    {   -9938,   23396 }, /* 237: -3.1392° */
    {  -10003,   23460 }, /* 238: -3.1680° */
    {  -10067,   23525 }, /* 239: -3.1968° */
    {  -10131,   23590 }, /* 240: -3.2256° */
    {  -10194,   23655 }, /* 241: -3.2544° */
    {  -10257,   23720 }, /* 242: -3.2832° */
    {  -10320,   23784 }, /* 243: -3.3120° */
    {  -10382,   23849 }, /* 244: -3.3408° */
    {  -10445,   23914 }, /* 245: -3.3696° */
    {  -10506,   23979 }, /* 246: -3.3984° */
    {  -10568,   24044 }, /* 247: -3.4272° */
    {  -10629,   24109 }, /* 248: -3.4560° */
    {  -10690,   24174 }, /* 249: -3.4848° */
    {  -10750,   24239 }, /* 250: -3.5136° */
    {  -10811,   24303 }, /* 251: -3.5424° */
    {  -10871,   24368 }, /* 252: -3.5712° */
    {  -10930,   24433 }, /* 253: -3.6000° */
    {  -10990,   24498 }, /* 254: -3.6288° */
    {  -11049,   24563 }  /* 255: -3.6576 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 39063 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 3906 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8198 }, /*   0:  2.3040° */
    {   32260,    8262 }, /*   1:  2.2860° */
    {   31761,    8326 }, /*   2:  2.2680° */
    {   31269,    8390 }, /*   3:  2.2500° */
    {   30785,    8454 }, /*   4:  2.2320° */
    {   30308,    8518 }, /*   5:  2.2140° */
    {   29838,    8582 }, /*   6:  2.1960° */
    {   29375,    8646 }, /*   7:  2.1780° */
    {   28918,    8710 }, /*   8:  2.1600° */
    {   28469,    8774 }, /*   9:  2.1420° */
    {   28026,    8838 }, /*  10:  2.1240° */
    {   27589,    8902 }, /*  11:  2.1060° */
    {   27158,    8965 }, /*  12:  2.0880° */
    {   26734,    9029 }, /*  13:  2.0700° */
    {   26315,    9093 }, /*  14:  2.0520° */
    {   25903,    9157 }, /*  15:  2.0340° */
    {   25496,    9221 }, /*  16:  2.0160° */
    {   25095,    9285 }, /*  17:  1.9980° */
    {   24699,    9349 }, /*  18:  1.9800° */
    {   24309,    9413 }, /*  19:  1.9620° */
    {   23923,    9477 }, /*  20:  1.9440° */
    {   23543,    9541 }, /*  21:  1.9260° */
    {   23168,    9605 }, /*  22:  1.9080° */
    {   22798,    9669 }, /*  23:  1.8900° */
    {   22433,    9733 }, /*  24:  1.8720° */
    {   22073,    9797 }, /*  25:  1.8540° */
    {   21717,    9861 }, /*  26:  1.8360° */
    {   21366,    9925 }, /*  27:  1.8180° */
    {   21019,    9988 }, /*  28:  1.8000° */
    {   20677,   10052 }, /*  29:  1.7820° */
    {   20339,   10116 }, /*  30:  1.7640° */
    {   20006,   10180 }, /*  31:  1.7460° */
    {   19676,   10244 }, /*  32:  1.7280° */
    {   19351,   10308 }, /*  33:  1.7100° */
    {   19029,   10372 }, /*  34:  1.6920° */
    {   18712,   10436 }, /*  35:  1.6740° */
    {   18398,   10500 }, /*  36:  1.6560° */
    {   18088,   10564 }, /*  37:  1.6380° */
    {   17782,   10628 }, /*  38:  1.6200° */
    {   17480,   10692 }, /*  39:  1.6020° */
    {   17181,   10756 }, /*  40:  1.5840° */
    {   16886,   10820 }, /*  41:  1.5660° */
    {   16594,   10883 }, /*  42:  1.5480° */
    {   16305,   10947 }, /*  43:  1.5300° */
    {   16020,   11011 }, /*  44:  1.5120° */
    {   15738,   11075 }, /*  45:  1.4940° */
    {   15460,   11139 }, /*  46:  1.4760° */
    {   15184,   11203 }, /*  47:  1.4580° */
    {   14912,   11267 }, /*  48:  1.4400° */
    {   14643,   11331 }, /*  49:  1.4220° */
    {   14377,   11395 }, /*  50:  1.4040° */
    {   14113,   11459 }, /*  51:  1.3860° */
    {   13853,   11523 }, /*  52:  1.3680° */
    {   13596,   11587 }, /*  53:  1.3500° */
    {   13341,   11651 }, /*  54:  1.3320° */
    {   13089,   11715 }, /*  55:  1.3140° */
    {   12840,   11779 }, /*  56:  1.2960° */
    {   12593,   11842 }, /*  57:  1.2780° */
    {   12350,   11906 }, /*  58:  1.2600° */
    {   12108,   11970 }, /*  59:  1.2420° */
    {   11870,   12034 }, /*  60:  1.2240° */
    {   11634,   12098 }, /*  61:  1.2060° */
    {   11400,   12162 }, /*  62:  1.1880° */
    {   11169,   12226 }, /*  63:  1.1700° */
    {   10940,   12290 }, /*  64:  1.1520° */
    {   10713,   12354 }, /*  65:  1.1340° */
    {   10489,   12418 }, /*  66:  1.1160° */
    {   10267,   12482 }, /*  67:  1.0980° */
    {   10048,   12546 }, /*  68:  1.0800° */
    {    9830,   12610 }, /*  69:  1.0620° */
    {    9615,   12674 }, /*  70:  1.0440° */
    {    9402,   12738 }, /*  71:  1.0260° */
    {    9191,   12801 }, /*  72:  1.0080° */
    {    8982,   12865 }, /*  73:  0.9900° */
    {    8776,   12929 }, /*  74:  0.9720° */
    {    8571,   12993 }, /*  75:  0.9540° */
    {    8368,   13057 }, /*  76:  0.9360° */
    {    8167,   13121 }, /*  77:  0.9180° */
    {    7969,   13185 }, /*  78:  0.9000° */
    {    7772,   13249 }, /*  79:  0.8820° */
    {    7577,   13313 }, /*  80:  0.8640° */
    {    7383,   13377 }, /*  81:  0.8460° */
    {    7192,   13441 }, /*  82:  0.8280° */
    {    7003,   13505 }, /*  83:  0.8100° */
    {    6815,   13569 }, /*  84:  0.7920° */
    {    6629,   13633 }, /*  85:  0.7740° */
    {    6445,   13697 }, /*  86:  0.7560° */
    {    6262,   13761 }, /*  87:  0.7380° */
    {    6081,   13825 }, /*  88:  0.7200° */
    {    5902,   13889 }, /*  89:  0.7020° */
    {    5724,   13953 }, /*  90:  0.6840° */
    {    5548,   14016 }, /*  91:  0.6660° */
    {    5374,   14080 }, /*  92:  0.6480° */
    {    5201,   14144 }, /*  93:  0.6300° */
    {    5030,   14208 }, /*  94:  0.6120° */
    {    4860,   14272 }, /*  95:  0.5940° */
    {    4692,   14336 }, /*  96:  0.5760° */
    {    4525,   14400 }, /*  97:  0.5580° */
    {    4360,   14464 }, /*  98:  0.5400° */
    {    4196,   14528 }, /*  99:  0.5220° */
    {    4034,   14592 }, /* 100:  0.5040° */
    {    3873,   14656 }, /* 101:  0.4860° */
    {    3713,   14720 }, /* 102:  0.4680° */
    {    3555,   14784 }, /* 103:  0.4500° */
    {    3398,   14848 }, /* 104:  0.4320° */
    {    3243,   14912 }, /* 105:  0.4140° */
    {    3088,   14976 }, /* 106:  0.3960° */
    {    2936,   15040 }, /* 107:  0.3780° */
    {    2784,   15104 }, /* 108:  0.3600° */
    {    2634,   15168 }, /* 109:  0.3420° */
    {    2485,   15232 }, /* 110:  0.3240° */
    {    2337,   15296 }, /* 111:  0.3060° */
    {    2190,   15360 }, /* 112:  0.2880° */
    {    2045,   15424 }, /* 113:  0.2700° */
    {    1901,   15488 }, /* 114:  0.2520° */
    {    1758,   15552 }, /* 115:  0.2340° */
    {    1616,   15616 }, /* 116:  0.2160° */
    {    1475,   15680 }, /* 117:  0.1980° */
    {    1335,   15744 }, /* 118:  0.1800° */
    {    1197,   15808 }, /* 119:  0.1620° */
    {    1060,   15872 }, /* 120:  0.1440° */
    {     923,   15936 }, /* 121:  0.1260° */
    {     788,   16000 }, /* 122:  0.1080° */
    {     654,   16064 }, /* 123:  0.0900° */
    {     521,   16128 }, /* 124:  0.0720° */
    {     389,   16192 }, /* 125:  0.0540° */
    {     258,   16256 }, /* 126:  0.0360° */
    {     128,   16320 }, /* 127:  0.0180° */
    {       0,   16384 }, /* 128:  0.0000° */
    {    -127,   16448 }, /* 129: -0.0180° */
    {    -254,   16512 }, /* 130: -0.0360° */
    {    -380,   16576 }, /* 131: -0.0540° */
    {    -505,   16640 }, /* 132: -0.0720° */
    {    -629,   16704 }, /* 133: -0.0900° */
    {    -752,   16768 }, /* 134: -0.1080° */
    {    -875,   16832 }, /* 135: -0.1260° */
    {    -996,   16896 }, /* 136: -0.1440° */
    {   -1116,   16960 }, /* 137: -0.1620° */
    {   -1236,   17024 }, /* 138: -0.1800° */
    {   -1354,   17088 }, /* 139: -0.1980° */
    {   -1472,   17152 }, /* 140: -0.2160° */
    {   -1589,   17216 }, /* 141: -0.2340° */
    {   -1705,   17280 }, /* 142: -0.2520° */
    {   -1820,   17344 }, /* 143: -0.2700° */
    {   -1934,   17408 }, /* 144: -0.2880° */
    {   -2048,   17472 }, /* 145: -0.3060° */
    {   -2160,   17536 }, /* 146: -0.3240° */
    {   -2272,   17600 }, /* 147: -0.3420° */
    {   -2383,   17664 }, /* 148: -0.3600° */
    {   -2493,   17728 }, /* 149: -0.3780° */
    {   -2603,   17792 }, /* 150: -0.3960° */
    {   -2711,   17856 }, /* 151: -0.4140° */
    {   -2819,   17920 }, /* 152: -0.4320° */
    {   -2926,   17984 }, /* 153: -0.4500° */
    {   -3032,   18048 }, /* 154: -0.4680° */
    {   -3138,   18112 }, /* 155: -0.4860° */
    {   -3243,   18176 }, /* 156: -0.5040° */
    {   -3347,   18240 }, /* 157: -0.5220° */
    {   -3450,   18304 }, /* 158: -0.5400° */
    {   -3553,   18368 }, /* 159: -0.5580° */
    {   -3655,   18432 }, /* 160: -0.5760° */
    {   -3756,   18497 }, /* 161: -0.5940° */
    {   -3857,   18561 }, /* 162: -0.6120° */
    {   -3957,   18625 }, /* 163: -0.6300° */
    {   -4056,   18689 }, /* 164: -0.6480° */
    {   -4155,   18753 }, /* 165: -0.6660° */
    {   -4253,   18817 }, /* 166: -0.6840° */
    {   -4350,   18881 }, /* 167: -0.7020° */
    {   -4447,   18945 }, /* 168: -0.7200° */
    {   -4542,   19009 }, /* 169: -0.7380° */
    {   -4638,   19073 }, /* 170: -0.7560° */
    {   -4732,   19137 }, /* 171: -0.7740° */
    {   -4826,   19201 }, /* 172: -0.7920° */
    {   -4920,   19265 }, /* 173: -0.8100° */
    {   -5013,   19330 }, /* 174: -0.8280° */
    {   -5105,   19394 }, /* 175: -0.8460° */
    {   -5197,   19458 }, /* 176: -0.8640° */
    {   -5288,   19522 }, /* 177: -0.8820° */
    {   -5378,   19586 }, /* 178: -0.9000° */
    {   -5468,   19650 }, /* 179: -0.9180° */
    {   -5557,   19714 }, /* 180: -0.9360° */
    {   -5646,   19778 }, /* 181: -0.9540° */
    {   -5734,   19842 }, /* 182: -0.9720° */
    {   -5821,   19907 }, /* 183: -0.9900° */
    {   -5908,   19971 }, /* 184: -1.0080° */
    {   -5995,   20035 }, /* 185: -1.0260° */
    {   -6081,   20099 }, /* 186: -1.0440° */
    {   -6166,   20163 }, /* 187: -1.0620° */
    {   -6251,   20227 }, /* 188: -1.0800° */
    {   -6335,   20291 }, /* 189: -1.0980° */
    {   -6419,   20355 }, /* 190: -1.1160° */
    {   -6502,   20420 }, /* 191: -1.1340° */
    {   -6585,   20484 }, /* 192: -1.1520° */
    {   -6667,   20548 }, /* 193: -1.1700° */
    {   -6749,   20612 }, /* 194: -1.1880° */
    {   -6830,   20676 }, /* 195: -1.2060° */
    {   -6911,   20740 }, /* 196: -1.2240° */
    {   -6991,   20804 }, /* 197: -1.2420° */
    {   -7071,   20869 }, /* 198: -1.2600° */
    {   -7150,   20933 }, /* 199: -1.2780° */
    {   -7229,   20997 }, /* 200: -1.2960° */
    {   -7307,   21061 }, /* 201: -1.3140° */
    {   -7385,   21125 }, /* 202: -1.3320° */
    {   -7463,   21189 }, /* 203: -1.3500° */
    {   -7540,   21254 }, /* 204: -1.3680° */
    {   -7616,   21318 }, /* 205: -1.3860° */
    {   -7692,   21382 }, /* 206: -1.4040° */
    {   -7768,   21446 }, /* 207: -1.4220° */
    {   -7843,   21510 }, /* 208: -1.4400° */
    {   -7917,   21575 }, /* 209: -1.4580° */
    {   -7992,   21639 }, /* 210: -1.4760° */
    {   -8065,   21703 }, /* 211: -1.4940° */
    {   -8139,   21767 }, /* 212: -1.5120° */
    {   -8212,   21831 }, /* 213: -1.5300° */
    {   -8284,   21896 }, /* 214: -1.5480° */
    {   -8356,   21960 }, /* 215: -1.5660° */
    {   -8428,   22024 }, /* 216: -1.5840° */
    {   -8499,   22088 }, /* 217: -1.6020° */
    {   -8570,   22152 }, /* 218: -1.6200° */
    {   -8641,   22217 }, /* 219: -1.6380° */
    {   -8711,   22281 }, /* 220: -1.6560° */
    {   -8781,   22345 }, /* 221: -1.6740° */
    {   -8850,   22409 }, /* 222: -1.6920° */
    {   -8919,   22474 }, /* 223: -1.7100° */
    {   -8987,   22538 }, /* 224: -1.7280° */
    {   -9055,   22602 }, /* 225: -1.7460° */
    {   -9123,   22666 }, /* 226: -1.7640° */
    {   -9190,   22731 }, /* 227: -1.7820° */
    {   -9257,   22795 }, /* 228: -1.8000° */
    {   -9324,   22859 }, /* 229: -1.8180° */
    {   -9390,   22923 }, /* 230: -1.8360° */
    {   -9456,   22988 }, /* 231: -1.8540° */
    {   -9522,   23052 }, /* 232: -1.8720° */
    {   -9587,   23116 }, /* 233: -1.8900° */
    {   -9652,   23181 }, /* 234: -1.9080° */
    {   -9716,   23245 }, /* 235: -1.9260° */
    {   -9780,   23309 }, /* 236: -1.9440° */
    {   -9844,   23373 }, /* 237: -1.9620° */
    {   -9908,   23438 }, /* 238: -1.9800° */
    {   -9971,   23502 }, /* 239: -1.9980° */
    {  -10033,   23566 }, /* 240: -2.0160° */
    {  -10096,   23631 }, /* 241: -2.0340° */
    {  -10158,   23695 }, /* 242: -2.0520° */
    {  -10220,   23759 }, /* 243: -2.0700° */
    {  -10281,   23824 }, /* 244: -2.0880° */
    {  -10342,   23888 }, /* 245: -2.1060° */
    {  -10403,   23952 }, /* 246: -2.1240° */
    {  -10463,   24017 }, /* 247: -2.1420° */
    {  -10524,   24081 }, /* 248: -2.1600° */
    {  -10583,   24145 }, /* 249: -2.1780° */
    {  -10643,   24210 }, /* 250: -2.1960° */
    {  -10702,   24274 }, /* 251: -2.2140° */
    {  -10761,   24338 }, /* 252: -2.2320° */
    {  -10820,   24403 }, /* 253: -2.2500° */
    {  -10878,   24467 }, /* 254: -2.2680° */
    {  -10936,   24531 }  /* 255: -2.2860 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 39063 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 3906 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8201 }, /*   0:  2.7648° */
    {   32261,    8265 }, /*   1:  2.7432° */
    {   31762,    8329 }, /*   2:  2.7216° */
    {   31270,    8393 }, /*   3:  2.7000° */
    {   30786,    8457 }, /*   4:  2.6784° */
    {   30309,    8521 }, /*   5:  2.6568° */
    {   29840,    8585 }, /*   6:  2.6352° */
    {   29377,    8648 }, /*   7:  2.6136° */
    {   28921,    8712 }, /*   8:  2.5920° */
    {   28472,    8776 }, /*   9:  2.5704° */
    {   28029,    8840 }, /*  10:  2.5488° */
    {   27592,    8904 }, /*  11:  2.5272° */
    {   27162,    8968 }, /*  12:  2.5056° */
    {   26738,    9032 }, /*  13:  2.4840° */
    {   26320,    9096 }, /*  14:  2.4624° */
    {   25907,    9160 }, /*  15:  2.4408° */
    {   25501,    9224 }, /*  16:  2.4192° */
    {   25099,    9288 }, /*  17:  2.3976° */
    {   24704,    9352 }, /*  18:  2.3760° */
    {   24314,    9415 }, /*  19:  2.3544° */
    {   23929,    9479 }, /*  20:  2.3328° */
    {   23549,    9543 }, /*  21:  2.3112° */
    {   23174,    9607 }, /*  22:  2.2896° */
    {   22804,    9671 }, /*  23:  2.2680° */
    {   22439,    9735 }, /*  24:  2.2464° */
    {   22079,    9799 }, /*  25:  2.2248° */
    {   21724,    9863 }, /*  26:  2.2032° */
    {   21373,    9927 }, /*  27:  2.1816° */
    {   21026,    9991 }, /*  28:  2.1600° */
    {   20684,   10055 }, /*  29:  2.1384° */
    {   20346,   10118 }, /*  30:  2.1168° */
    {   20013,   10182 }, /*  31:  2.0952° */
    {   19683,   10246 }, /*  32:  2.0736° */
    {   19358,   10310 }, /*  33:  2.0520° */
    {   19037,   10374 }, /*  34:  2.0304° */
    {   18719,   10438 }, /*  35:  2.0088° */
    {   18406,   10502 }, /*  36:  1.9872° */
    {   18096,   10566 }, /*  37:  1.9656° */
    {   17790,   10630 }, /*  38:  1.9440° */
    {   17488,   10694 }, /*  39:  1.9224° */
    {   17189,   10757 }, /*  40:  1.9008° */
    {   16893,   10821 }, /*  41:  1.8792° */
    {   16602,   10885 }, /*  42:  1.8576° */
    {   16313,   10949 }, /*  43:  1.8360° */
    {   16028,   11013 }, /*  44:  1.8144° */
    {   15746,   11077 }, /*  45:  1.7928° */
    {   15468,   11141 }, /*  46:  1.7712° */
    {   15192,   11205 }, /*  47:  1.7496° */
    {   14920,   11269 }, /*  48:  1.7280° */
    {   14651,   11333 }, /*  49:  1.7064° */
    {   14385,   11396 }, /*  50:  1.6848° */
    {   14121,   11460 }, /*  51:  1.6632° */
    {   13861,   11524 }, /*  52:  1.6416° */
    {   13604,   11588 }, /*  53:  1.6200° */
    {   13349,   11652 }, /*  54:  1.5984° */
    {   13097,   11716 }, /*  55:  1.5768° */
    {   12848,   11780 }, /*  56:  1.5552° */
    {   12601,   11844 }, /*  57:  1.5336° */
    {   12358,   11908 }, /*  58:  1.5120° */
    {   12116,   11972 }, /*  59:  1.4904° */
    {   11878,   12035 }, /*  60:  1.4688° */
    {   11641,   12099 }, /*  61:  1.4472° */
    {   11408,   12163 }, /*  62:  1.4256° */
    {   11177,   12227 }, /*  63:  1.4040° */
    {   10948,   12291 }, /*  64:  1.3824° */
    {   10721,   12355 }, /*  65:  1.3608° */
    {   10497,   12419 }, /*  66:  1.3392° */
    {   10275,   12483 }, /*  67:  1.3176° */
    {   10055,   12547 }, /*  68:  1.2960° */
    {    9838,   12611 }, /*  69:  1.2744° */
    {    9623,   12675 }, /*  70:  1.2528° */
    {    9410,   12738 }, /*  71:  1.2312° */
    {    9199,   12802 }, /*  72:  1.2096° */
    {    8990,   12866 }, /*  73:  1.1880° */
    {    8783,   12930 }, /*  74:  1.1664° */
    {    8578,   12994 }, /*  75:  1.1448° */
    {    8375,   13058 }, /*  76:  1.1232° */
    {    8174,   13122 }, /*  77:  1.1016° */
    {    7976,   13186 }, /*  78:  1.0800° */
    {    7779,   13250 }, /*  79:  1.0584° */
    {    7583,   13314 }, /*  80:  1.0368° */
    {    7390,   13378 }, /*  81:  1.0152° */
    {    7199,   13442 }, /*  82:  0.9936° */
    {    7009,   13505 }, /*  83:  0.9720° */
    {    6821,   13569 }, /*  84:  0.9504° */
    {    6635,   13633 }, /*  85:  0.9288° */
    {    6451,   13697 }, /*  86:  0.9072° */
    {    6268,   13761 }, /*  87:  0.8856° */
    {    6087,   13825 }, /*  88:  0.8640° */
    {    5908,   13889 }, /*  89:  0.8424° */
    {    5730,   13953 }, /*  90:  0.8208° */
    {    5554,   14017 }, /*  91:  0.7992° */
    {    5380,   14081 }, /*  92:  0.7776° */
    {    5207,   14145 }, /*  93:  0.7560° */
    {    5035,   14209 }, /*  94:  0.7344° */
    {    4865,   14273 }, /*  95:  0.7128° */
    {    4697,   14337 }, /*  96:  0.6912° */
    {    4530,   14400 }, /*  97:  0.6696° */
    {    4365,   14464 }, /*  98:  0.6480° */
    {    4201,   14528 }, /*  99:  0.6264° */
    {    4038,   14592 }, /* 100:  0.6048° */
    {    3877,   14656 }, /* 101:  0.5832° */
    {    3717,   14720 }, /* 102:  0.5616° */
    {    3559,   14784 }, /* 103:  0.5400° */
    {    3402,   14848 }, /* 104:  0.5184° */
    {    3247,   14912 }, /* 105:  0.4968° */
    {    3092,   14976 }, /* 106:  0.4752° */
    {    2939,   15040 }, /* 107:  0.4536° */
    {    2787,   15104 }, /* 108:  0.4320° */
    {    2637,   15168 }, /* 109:  0.4104° */
    {    2488,   15232 }, /* 110:  0.3888° */
    {    2340,   15296 }, /* 111:  0.3672° */
    {    2193,   15360 }, /* 112:  0.3456° */
    {    2047,   15424 }, /* 113:  0.3240° */
    {    1903,   15488 }, /* 114:  0.3024° */
    {    1760,   15552 }, /* 115:  0.2808° */
    {    1618,   15616 }, /* 116:  0.2592° */
    {    1477,   15680 }, /* 117:  0.2376° */
    {    1337,   15744 }, /* 118:  0.2160° */
    {    1199,   15808 }, /* 119:  0.1944° */
    {    1061,   15872 }, /* 120:  0.1728° */
    {     925,   15936 }, /* 121:  0.1512° */
    {     789,   16000 }, /* 122:  0.1296° */
    {     655,   16064 }, /* 123:  0.1080° */
    {     522,   16128 }, /* 124:  0.0864° */
    {     390,   16192 }, /* 125:  0.0648° */
    {     259,   16256 }, /* 126:  0.0432° */
    {     129,   16320 }, /* 127:  0.0216° */
    {       0,   16383 }, /* 128: -0.0000° */
    {    -128,   16448 }, /* 129: -0.0216° */
    {    -255,   16512 }, /* 130: -0.0432° */
    {    -381,   16576 }, /* 131: -0.0648° */
    {    -506,   16640 }, /* 132: -0.0864° */
    {    -630,   16704 }, /* 133: -0.1080° */
    {    -754,   16768 }, /* 134: -0.1296° */
    {    -876,   16832 }, /* 135: -0.1512° */
    {    -997,   16896 }, /* 136: -0.1728° */
    {   -1118,   16960 }, /* 137: -0.1944° */
    {   -1238,   17024 }, /* 138: -0.2160° */
    {   -1356,   17088 }, /* 139: -0.2376° */
    {   -1474,   17152 }, /* 140: -0.2592° */
    {   -1591,   17216 }, /* 141: -0.2808° */
    {   -1707,   17280 }, /* 142: -0.3024° */
    {   -1823,   17344 }, /* 143: -0.3240° */
    {   -1937,   17408 }, /* 144: -0.3456° */
    {   -2051,   17472 }, /* 145: -0.3672° */
    {   -2164,   17536 }, /* 146: -0.3888° */
    {   -2276,   17600 }, /* 147: -0.4104° */
    {   -2387,   17664 }, /* 148: -0.4320° */
    {   -2497,   17728 }, /* 149: -0.4536° */
    {   -2607,   17792 }, /* 150: -0.4752° */
    {   -2716,   17856 }, /* 151: -0.4968° */
    {   -2824,   17920 }, /* 152: -0.5184° */
    {   -2931,   17984 }, /* 153: -0.5400° */
    {   -3038,   18048 }, /* 154: -0.5616° */
    {   -3144,   18112 }, /* 155: -0.5832° */
    {   -3249,   18177 }, /* 156: -0.6048° */
    {   -3353,   18241 }, /* 157: -0.6264° */
    {   -3457,   18305 }, /* 158: -0.6480° */
    {   -3559,   18369 }, /* 159: -0.6696° */
    {   -3662,   18433 }, /* 160: -0.6912° */
    {   -3763,   18497 }, /* 161: -0.7128° */
    {   -3864,   18561 }, /* 162: -0.7344° */
    {   -3964,   18625 }, /* 163: -0.7560° */
    {   -4064,   18689 }, /* 164: -0.7776° */
    {   -4162,   18753 }, /* 165: -0.7992° */
    {   -4261,   18817 }, /* 166: -0.8208° */
    {   -4358,   18882 }, /* 167: -0.8424° */
    {   -4455,   18946 }, /* 168: -0.8640° */
    {   -4551,   19010 }, /* 169: -0.8856° */
    {   -4647,   19074 }, /* 170: -0.9072° */
    {   -4741,   19138 }, /* 171: -0.9288° */
    {   -4836,   19202 }, /* 172: -0.9504° */
    {   -4929,   19266 }, /* 173: -0.9720° */
    {   -5022,   19330 }, /* 174: -0.9936° */
    {   -5115,   19395 }, /* 175: -1.0152° */
    {   -5207,   19459 }, /* 176: -1.0368° */
    {   -5298,   19523 }, /* 177: -1.0584° */
    {   -5389,   19587 }, /* 178: -1.0800° */
    {   -5479,   19651 }, /* 179: -1.1016° */
    {   -5568,   19715 }, /* 180: -1.1232° */
    {   -5657,   19779 }, /* 181: -1.1448° */
    {   -5746,   19844 }, /* 182: -1.1664° */
    {   -5833,   19908 }, /* 183: -1.1880° */
    {   -5921,   19972 }, /* 184: -1.2096° */
    {   -6007,   20036 }, /* 185: -1.2312° */
    {   -6093,   20100 }, /* 186: -1.2528° */
    {   -6179,   20165 }, /* 187: -1.2744° */
    {   -6264,   20229 }, /* 188: -1.2960° */
    {   -6349,   20293 }, /* 189: -1.3176° */
    {   -6433,   20357 }, /* 190: -1.3392° */
    {   -6516,   20421 }, /* 191: -1.3608° */
    {   -6599,   20486 }, /* 192: -1.3824° */
    {   -6682,   20550 }, /* 193: -1.4040° */
    {   -6764,   20614 }, /* 194: -1.4256° */
    {   -6845,   20678 }, /* 195: -1.4472° */
    {   -6926,   20742 }, /* 196: -1.4688° */
    {   -7007,   20807 }, /* 197: -1.4904° */
    {   -7087,   20871 }, /* 198: -1.5120° */
    {   -7166,   20935 }, /* 199: -1.5336° */
    {   -7245,   20999 }, /* 200: -1.5552° */
    {   -7324,   21064 }, /* 201: -1.5768° */
    {   -7402,   21128 }, /* 202: -1.5984° */
    {   -7480,   21192 }, /* 203: -1.6200° */
    {   -7557,   21256 }, /* 204: -1.6416° */
    {   -7634,   21321 }, /* 205: -1.6632° */
    {   -7710,   21385 }, /* 206: -1.6848° */
    {   -7786,   21449 }, /* 207: -1.7064° */
    {   -7861,   21513 }, /* 208: -1.7280° */
    {   -7936,   21578 }, /* 209: -1.7496° */
    {   -8010,   21642 }, /* 210: -1.7712° */
    {   -8085,   21706 }, /* 211: -1.7928° */
    {   -8158,   21771 }, /* 212: -1.8144° */
    {   -8231,   21835 }, /* 213: -1.8360° */
    {   -8304,   21899 }, /* 214: -1.8576° */
    {   -8377,   21963 }, /* 215: -1.8792° */
    {   -8449,   22028 }, /* 216: -1.9008° */
    {   -8520,   22092 }, /* 217: -1.9224° */
    {   -8591,   22156 }, /* 218: -1.9440° */
    {   -8662,   22221 }, /* 219: -1.9656° */
    {   -8732,   22285 }, /* 220: -1.9872° */
    {   -8802,   22349 }, /* 221: -2.0088° */
    {   -8872,   22414 }, /* 222: -2.0304° */
    {   -8941,   22478 }, /* 223: -2.0520° */
    {   -9010,   22542 }, /* 224: -2.0736° */
    {   -9078,   22607 }, /* 225: -2.0952° */
    {   -9146,   22671 }, /* 226: -2.1168° */
    {   -9214,   22736 }, /* 227: -2.1384° */
    {   -9281,   22800 }, /* 228: -2.1600° */
    {   -9348,   22864 }, /* 229: -2.1816° */
    {   -9415,   22929 }, /* 230: -2.2032° */
    {   -9481,   22993 }, /* 231: -2.2248° */
    {   -9547,   23058 }, /* 232: -2.2464° */
    {   -9612,   23122 }, /* 233: -2.2680° */
    {   -9677,   23186 }, /* 234: -2.2896° */
    {   -9742,   23251 }, /* 235: -2.3112° */
    {   -9806,   23315 }, /* 236: -2.3328° */
    {   -9870,   23380 }, /* 237: -2.3544° */
    {   -9934,   23444 }, /* 238: -2.3760° */
    {   -9997,   23508 }, /* 239: -2.3976° */
    {  -10061,   23573 }, /* 240: -2.4192° */
    {  -10123,   23637 }, /* 241: -2.4408° */
    {  -10186,   23702 }, /* 242: -2.4624° */
    {  -10248,   23766 }, /* 243: -2.4840° */
    {  -10309,   23831 }, /* 244: -2.5056° */
    {  -10371,   23895 }, /* 245: -2.5272° */
    {  -10432,   23960 }, /* 246: -2.5488° */
    {  -10493,   24024 }, /* 247: -2.5704° */
    {  -10553,   24089 }, /* 248: -2.5920° */
    {  -10613,   24153 }, /* 249: -2.6136° */
    {  -10673,   24218 }, /* 250: -2.6352° */
    {  -10732,   24282 }, /* 251: -2.6568° */
    {  -10792,   24347 }, /* 252: -2.6784° */
    {  -10850,   24411 }, /* 253: -2.7000° */
    {  -10909,   24476 }, /* 254: -2.7216° */
    {  -10967,   24540 }  /* 255: -2.7432 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 40000 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 4000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8198 }, /*   0:  2.2500° */
    {   32260,    8262 }, /*   1:  2.2324° */
    {   31761,    8326 }, /*   2:  2.2148° */
    {   31269,    8390 }, /*   3:  2.1973° */
    {   30785,    8454 }, /*   4:  2.1797° */
    {   30307,    8518 }, /*   5:  2.1621° */
    {   29837,    8582 }, /*   6:  2.1445° */
    {   29374,    8645 }, /*   7:  2.1270° */
    {   28918,    8709 }, /*   8:  2.1094° */
    {   28468,    8773 }, /*   9:  2.0918° */
    {   28025,    8837 }, /*  10:  2.0742° */
    {   27589,    8901 }, /*  11:  2.0566° */
    {   27158,    8965 }, /*  12:  2.0391° */
    {   26734,    9029 }, /*  13:  2.0215° */
    {   26315,    9093 }, /*  14:  2.0039° */
    {   25902,    9157 }, /*  15:  1.9863° */
    {   25496,    9221 }, /*  16:  1.9687° */
    {   25094,    9285 }, /*  17:  1.9512° */
    {   24698,    9349 }, /*  18:  1.9336° */
    {   24308,    9413 }, /*  19:  1.9160° */
    {   23923,    9477 }, /*  20:  1.8984° */
    {   23543,    9541 }, /*  21:  1.8809° */
    {   23168,    9605 }, /*  22:  1.8633° */
    {   22798,    9669 }, /*  23:  1.8457° */
    {   22433,    9732 }, /*  24:  1.8281° */
    {   22072,    9796 }, /*  25:  1.8105° */
    {   21717,    9860 }, /*  26:  1.7930° */
    {   21365,    9924 }, /*  27:  1.7754° */
    {   21019,    9988 }, /*  28:  1.7578° */
    {   20677,   10052 }, /*  29:  1.7402° */
    {   20339,   10116 }, /*  30:  1.7227° */
    {   20005,   10180 }, /*  31:  1.7051° */
    {   19675,   10244 }, /*  32:  1.6875° */
    {   19350,   10308 }, /*  33:  1.6699° */
    {   19029,   10372 }, /*  34:  1.6523° */
    {   18711,   10436 }, /*  35:  1.6348° */
    {   18397,   10500 }, /*  36:  1.6172° */
    {   18088,   10564 }, /*  37:  1.5996° */
    {   17782,   10628 }, /*  38:  1.5820° */
    {   17479,   10691 }, /*  39:  1.5645° */
    {   17180,   10755 }, /*  40:  1.5469° */
    {   16885,   10819 }, /*  41:  1.5293° */
    {   16593,   10883 }, /*  42:  1.5117° */
    {   16305,   10947 }, /*  43:  1.4941° */
    {   16019,   11011 }, /*  44:  1.4766° */
    {   15738,   11075 }, /*  45:  1.4590° */
    {   15459,   11139 }, /*  46:  1.4414° */
    {   15184,   11203 }, /*  47:  1.4238° */
    {   14911,   11267 }, /*  48:  1.4062° */
    {   14642,   11331 }, /*  49:  1.3887° */
    {   14376,   11395 }, /*  50:  1.3711° */
    {   14113,   11459 }, /*  51:  1.3535° */
    {   13852,   11523 }, /*  52:  1.3359° */
    {   13595,   11587 }, /*  53:  1.3184° */
    {   13340,   11651 }, /*  54:  1.3008° */
    {   13088,   11714 }, /*  55:  1.2832° */
    {   12839,   11778 }, /*  56:  1.2656° */
    {   12593,   11842 }, /*  57:  1.2480° */
    {   12349,   11906 }, /*  58:  1.2305° */
    {   12108,   11970 }, /*  59:  1.2129° */
    {   11869,   12034 }, /*  60:  1.1953° */
    {   11633,   12098 }, /*  61:  1.1777° */
    {   11399,   12162 }, /*  62:  1.1602° */
    {   11168,   12226 }, /*  63:  1.1426° */
    {   10939,   12290 }, /*  64:  1.1250° */
    {   10713,   12354 }, /*  65:  1.1074° */
    {   10488,   12418 }, /*  66:  1.0898° */
    {   10267,   12482 }, /*  67:  1.0723° */
    {   10047,   12546 }, /*  68:  1.0547° */
    {    9830,   12610 }, /*  69:  1.0371° */
    {    9614,   12674 }, /*  70:  1.0195° */
    {    9401,   12737 }, /*  71:  1.0020° */
    {    9190,   12801 }, /*  72:  0.9844° */
    {    8982,   12865 }, /*  73:  0.9668° */
    {    8775,   12929 }, /*  74:  0.9492° */
    {    8570,   12993 }, /*  75:  0.9316° */
    {    8367,   13057 }, /*  76:  0.9141° */
    {    8167,   13121 }, /*  77:  0.8965° */
    {    7968,   13185 }, /*  78:  0.8789° */
    {    7771,   13249 }, /*  79:  0.8613° */
    {    7576,   13313 }, /*  80:  0.8437° */
    {    7383,   13377 }, /*  81:  0.8262° */
    {    7191,   13441 }, /*  82:  0.8086° */
    {    7002,   13505 }, /*  83:  0.7910° */
    {    6814,   13569 }, /*  84:  0.7734° */
    {    6628,   13633 }, /*  85:  0.7559° */
    {    6444,   13697 }, /*  86:  0.7383° */
    {    6261,   13761 }, /*  87:  0.7207° */
    {    6081,   13825 }, /*  88:  0.7031° */
    {    5901,   13888 }, /*  89:  0.6855° */
    {    5724,   13952 }, /*  90:  0.6680° */
    {    5548,   14016 }, /*  91:  0.6504° */
    {    5373,   14080 }, /*  92:  0.6328° */
    {    5201,   14144 }, /*  93:  0.6152° */
    {    5029,   14208 }, /*  94:  0.5977° */
    {    4860,   14272 }, /*  95:  0.5801° */
    {    4691,   14336 }, /*  96:  0.5625° */
    {    4525,   14400 }, /*  97:  0.5449° */
    {    4360,   14464 }, /*  98:  0.5273° */
    {    4196,   14528 }, /*  99:  0.5098° */
    {    4033,   14592 }, /* 100:  0.4922° */
    {    3872,   14656 }, /* 101:  0.4746° */
    {    3713,   14720 }, /* 102:  0.4570° */
    {    3555,   14784 }, /* 103:  0.4395° */
    {    3398,   14848 }, /* 104:  0.4219° */
    {    3242,   14912 }, /* 105:  0.4043° */
    {    3088,   14976 }, /* 106:  0.3867° */
    {    2935,   15040 }, /* 107:  0.3691° */
    {    2784,   15104 }, /* 108:  0.3516° */
    {    2633,   15168 }, /* 109:  0.3340° */
    {    2484,   15232 }, /* 110:  0.3164° */
    {    2337,   15296 }, /* 111:  0.2988° */
    {    2190,   15360 }, /* 112:  0.2812° */
    {    2045,   15424 }, /* 113:  0.2637° */
    {    1900,   15488 }, /* 114:  0.2461° */
    {    1757,   15552 }, /* 115:  0.2285° */
    {    1616,   15616 }, /* 116:  0.2109° */
    {    1475,   15680 }, /* 117:  0.1934° */
    {    1335,   15744 }, /* 118:  0.1758° */
    {    1197,   15808 }, /* 119:  0.1582° */
    {    1060,   15872 }, /* 120:  0.1406° */
    {     923,   15936 }, /* 121:  0.1230° */
    {     788,   16000 }, /* 122:  0.1055° */
    {     654,   16064 }, /* 123:  0.0879° */
    {     521,   16128 }, /* 124:  0.0703° */
    {     389,   16192 }, /* 125:  0.0527° */
    {     258,   16256 }, /* 126:  0.0352° */
    {     128,   16320 }, /* 127:  0.0176° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0176° */
    {    -254,   16512 }, /* 130: -0.0352° */
    {    -380,   16576 }, /* 131: -0.0527° */
    {    -505,   16640 }, /* 132: -0.0703° */
    {    -629,   16704 }, /* 133: -0.0879° */
    {    -752,   16768 }, /* 134: -0.1055° */
    {    -875,   16832 }, /* 135: -0.1230° */
    {    -996,   16896 }, /* 136: -0.1406° */
    {   -1116,   16960 }, /* 137: -0.1582° */
    {   -1235,   17024 }, /* 138: -0.1758° */
    {   -1354,   17088 }, /* 139: -0.1934° */
    {   -1472,   17152 }, /* 140: -0.2109° */
    {   -1588,   17216 }, /* 141: -0.2285° */
    {   -1704,   17280 }, /* 142: -0.2461° */
    {   -1820,   17344 }, /* 143: -0.2637° */
    {   -1934,   17408 }, /* 144: -0.2813° */
    {   -2047,   17472 }, /* 145: -0.2988° */
    {   -2160,   17536 }, /* 146: -0.3164° */
    {   -2272,   17600 }, /* 147: -0.3340° */
    {   -2382,   17664 }, /* 148: -0.3516° */
    {   -2493,   17728 }, /* 149: -0.3691° */
    {   -2602,   17792 }, /* 150: -0.3867° */
    {   -2711,   17856 }, /* 151: -0.4043° */
    {   -2819,   17920 }, /* 152: -0.4219° */
    {   -2926,   17984 }, /* 153: -0.4395° */
    {   -3032,   18048 }, /* 154: -0.4570° */
    {   -3137,   18112 }, /* 155: -0.4746° */
    {   -3242,   18176 }, /* 156: -0.4922° */
    {   -3346,   18240 }, /* 157: -0.5098° */
    {   -3450,   18304 }, /* 158: -0.5273° */
    {   -3553,   18368 }, /* 159: -0.5449° */
    {   -3654,   18432 }, /* 160: -0.5625° */
    {   -3756,   18496 }, /* 161: -0.5801° */
    {   -3856,   18561 }, /* 162: -0.5977° */
    {   -3956,   18625 }, /* 163: -0.6152° */
    {   -4055,   18689 }, /* 164: -0.6328° */
    {   -4154,   18753 }, /* 165: -0.6504° */
    {   -4252,   18817 }, /* 166: -0.6680° */
    {   -4349,   18881 }, /* 167: -0.6855° */
    {   -4446,   18945 }, /* 168: -0.7031° */
    {   -4542,   19009 }, /* 169: -0.7207° */
    {   -4637,   19073 }, /* 170: -0.7383° */
    {   -4731,   19137 }, /* 171: -0.7559° */
    {   -4825,   19201 }, /* 172: -0.7734° */
    {   -4919,   19265 }, /* 173: -0.7910° */
    {   -5012,   19329 }, /* 174: -0.8086° */
    {   -5104,   19394 }, /* 175: -0.8262° */
    {   -5195,   19458 }, /* 176: -0.8438° */
    {   -5286,   19522 }, /* 177: -0.8613° */
    {   -5377,   19586 }, /* 178: -0.8789° */
    {   -5467,   19650 }, /* 179: -0.8965° */
    {   -5556,   19714 }, /* 180: -0.9141° */
    {   -5645,   19778 }, /* 181: -0.9316° */
    {   -5733,   19842 }, /* 182: -0.9492° */
    {   -5820,   19906 }, /* 183: -0.9668° */
    {   -5907,   19970 }, /* 184: -0.9844° */
    {   -5994,   20035 }, /* 185: -1.0020° */
    {   -6079,   20099 }, /* 186: -1.0195° */
    {   -6165,   20163 }, /* 187: -1.0371° */
    {   -6250,   20227 }, /* 188: -1.0547° */
    {   -6334,   20291 }, /* 189: -1.0723° */
    {   -6418,   20355 }, /* 190: -1.0898° */
    {   -6501,   20419 }, /* 191: -1.1074° */
    {   -6584,   20483 }, /* 192: -1.1250° */
    {   -6666,   20548 }, /* 193: -1.1426° */
    {   -6747,   20612 }, /* 194: -1.1602° */
    {   -6829,   20676 }, /* 195: -1.1777° */
    {   -6909,   20740 }, /* 196: -1.1953° */
    {   -6990,   20804 }, /* 197: -1.2129° */
    {   -7069,   20868 }, /* 198: -1.2305° */
    {   -7149,   20933 }, /* 199: -1.2480° */
    {   -7227,   20997 }, /* 200: -1.2656° */
    {   -7306,   21061 }, /* 201: -1.2832° */
    {   -7383,   21125 }, /* 202: -1.3008° */
    {   -7461,   21189 }, /* 203: -1.3184° */
    {   -7538,   21253 }, /* 204: -1.3359° */
    {   -7614,   21318 }, /* 205: -1.3535° */
    {   -7690,   21382 }, /* 206: -1.3711° */
    {   -7766,   21446 }, /* 207: -1.3887° */
    {   -7841,   21510 }, /* 208: -1.4063° */
    {   -7915,   21574 }, /* 209: -1.4238° */
    {   -7990,   21638 }, /* 210: -1.4414° */
    {   -8063,   21703 }, /* 211: -1.4590° */
    {   -8137,   21767 }, /* 212: -1.4766° */
    {   -8210,   21831 }, /* 213: -1.4941° */
    {   -8282,   21895 }, /* 214: -1.5117° */
    {   -8354,   21959 }, /* 215: -1.5293° */
    {   -8426,   22024 }, /* 216: -1.5469° */
    {   -8497,   22088 }, /* 217: -1.5645° */
    {   -8568,   22152 }, /* 218: -1.5820° */
    {   -8638,   22216 }, /* 219: -1.5996° */
    {   -8709,   22280 }, /* 220: -1.6172° */
    {   -8778,   22345 }, /* 221: -1.6348° */
    {   -8847,   22409 }, /* 222: -1.6523° */
    {   -8916,   22473 }, /* 223: -1.6699° */
    {   -8985,   22537 }, /* 224: -1.6875° */
    {   -9053,   22602 }, /* 225: -1.7051° */
    {   -9121,   22666 }, /* 226: -1.7227° */
    {   -9188,   22730 }, /* 227: -1.7402° */
    {   -9255,   22794 }, /* 228: -1.7578° */
    {   -9322,   22859 }, /* 229: -1.7754° */
    {   -9388,   22923 }, /* 230: -1.7930° */
    {   -9454,   22987 }, /* 231: -1.8105° */
    {   -9519,   23051 }, /* 232: -1.8281° */
    {   -9584,   23116 }, /* 233: -1.8457° */
    {   -9649,   23180 }, /* 234: -1.8633° */
    {   -9714,   23244 }, /* 235: -1.8809° */
    {   -9778,   23308 }, /* 236: -1.8984° */
    {   -9841,   23373 }, /* 237: -1.9160° */
    {   -9905,   23437 }, /* 238: -1.9336° */
    {   -9968,   23501 }, /* 239: -1.9512° */
    {  -10031,   23566 }, /* 240: -1.9688° */
    {  -10093,   23630 }, /* 241: -1.9863° */
    {  -10155,   23694 }, /* 242: -2.0039° */
    {  -10217,   23758 }, /* 243: -2.0215° */
    {  -10278,   23823 }, /* 244: -2.0391° */
    {  -10339,   23887 }, /* 245: -2.0566° */
    {  -10400,   23951 }, /* 246: -2.0742° */
    {  -10460,   24016 }, /* 247: -2.0918° */
    {  -10520,   24080 }, /* 248: -2.1094° */
    {  -10580,   24144 }, /* 249: -2.1270° */
    {  -10640,   24209 }, /* 250: -2.1445° */
    {  -10699,   24273 }, /* 251: -2.1621° */
    {  -10758,   24337 }, /* 252: -2.1797° */
    {  -10816,   24402 }, /* 253: -2.1973° */
    {  -10875,   24466 }, /* 254: -2.2148° */
    {  -10932,   24530 }  /* 255: -2.2324 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 40000 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 4000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8201 }, /*   0:  2.7000° */
    {   32261,    8265 }, /*   1:  2.6789° */
    {   31761,    8328 }, /*   2:  2.6578° */
    {   31270,    8392 }, /*   3:  2.6367° */
    {   30786,    8456 }, /*   4:  2.6156° */
    {   30309,    8520 }, /*   5:  2.5945° */
    {   29839,    8584 }, /*   6:  2.5734° */
    {   29376,    8648 }, /*   7:  2.5523° */
    {   28920,    8712 }, /*   8:  2.5312° */
    {   28471,    8776 }, /*   9:  2.5102° */
    {   28028,    8840 }, /*  10:  2.4891° */
    {   27592,    8904 }, /*  11:  2.4680° */
    {   27161,    8968 }, /*  12:  2.4469° */
    {   26737,    9032 }, /*  13:  2.4258° */
    {   26319,    9096 }, /*  14:  2.4047° */
    {   25907,    9159 }, /*  15:  2.3836° */
    {   25500,    9223 }, /*  16:  2.3625° */
    {   25099,    9287 }, /*  17:  2.3414° */
    {   24703,    9351 }, /*  18:  2.3203° */
    {   24313,    9415 }, /*  19:  2.2992° */
    {   23928,    9479 }, /*  20:  2.2781° */
    {   23548,    9543 }, /*  21:  2.2570° */
    {   23173,    9607 }, /*  22:  2.2359° */
    {   22803,    9671 }, /*  23:  2.2148° */
    {   22438,    9735 }, /*  24:  2.1937° */
    {   22078,    9799 }, /*  25:  2.1727° */
    {   21723,    9862 }, /*  26:  2.1516° */
    {   21372,    9926 }, /*  27:  2.1305° */
    {   21025,    9990 }, /*  28:  2.1094° */
    {   20683,   10054 }, /*  29:  2.0883° */
    {   20345,   10118 }, /*  30:  2.0672° */
    {   20012,   10182 }, /*  31:  2.0461° */
    {   19682,   10246 }, /*  32:  2.0250° */
    {   19357,   10310 }, /*  33:  2.0039° */
    {   19035,   10374 }, /*  34:  1.9828° */
    {   18718,   10438 }, /*  35:  1.9617° */
    {   18405,   10502 }, /*  36:  1.9406° */
    {   18095,   10565 }, /*  37:  1.9195° */
    {   17789,   10629 }, /*  38:  1.8984° */
    {   17486,   10693 }, /*  39:  1.8773° */
    {   17188,   10757 }, /*  40:  1.8562° */
    {   16892,   10821 }, /*  41:  1.8352° */
    {   16600,   10885 }, /*  42:  1.8141° */
    {   16312,   10949 }, /*  43:  1.7930° */
    {   16027,   11013 }, /*  44:  1.7719° */
    {   15745,   11077 }, /*  45:  1.7508° */
    {   15467,   11141 }, /*  46:  1.7297° */
    {   15191,   11204 }, /*  47:  1.7086° */
    {   14919,   11268 }, /*  48:  1.6875° */
    {   14650,   11332 }, /*  49:  1.6664° */
    {   14383,   11396 }, /*  50:  1.6453° */
    {   14120,   11460 }, /*  51:  1.6242° */
    {   13860,   11524 }, /*  52:  1.6031° */
    {   13602,   11588 }, /*  53:  1.5820° */
    {   13348,   11652 }, /*  54:  1.5609° */
    {   13096,   11716 }, /*  55:  1.5398° */
    {   12847,   11780 }, /*  56:  1.5187° */
    {   12600,   11844 }, /*  57:  1.4977° */
    {   12356,   11907 }, /*  58:  1.4766° */
    {   12115,   11971 }, /*  59:  1.4555° */
    {   11876,   12035 }, /*  60:  1.4344° */
    {   11640,   12099 }, /*  61:  1.4133° */
    {   11407,   12163 }, /*  62:  1.3922° */
    {   11175,   12227 }, /*  63:  1.3711° */
    {   10946,   12291 }, /*  64:  1.3500° */
    {   10720,   12355 }, /*  65:  1.3289° */
    {   10496,   12419 }, /*  66:  1.3078° */
    {   10274,   12483 }, /*  67:  1.2867° */
    {   10054,   12547 }, /*  68:  1.2656° */
    {    9837,   12610 }, /*  69:  1.2445° */
    {    9622,   12674 }, /*  70:  1.2234° */
    {    9408,   12738 }, /*  71:  1.2023° */
    {    9197,   12802 }, /*  72:  1.1812° */
    {    8989,   12866 }, /*  73:  1.1602° */
    {    8782,   12930 }, /*  74:  1.1391° */
    {    8577,   12994 }, /*  75:  1.1180° */
    {    8374,   13058 }, /*  76:  1.0969° */
    {    8173,   13122 }, /*  77:  1.0758° */
    {    7974,   13186 }, /*  78:  1.0547° */
    {    7777,   13250 }, /*  79:  1.0336° */
    {    7582,   13314 }, /*  80:  1.0125° */
    {    7389,   13378 }, /*  81:  0.9914° */
    {    7198,   13441 }, /*  82:  0.9703° */
    {    7008,   13505 }, /*  83:  0.9492° */
    {    6820,   13569 }, /*  84:  0.9281° */
    {    6634,   13633 }, /*  85:  0.9070° */
    {    6450,   13697 }, /*  86:  0.8859° */
    {    6267,   13761 }, /*  87:  0.8648° */
    {    6086,   13825 }, /*  88:  0.8437° */
    {    5907,   13889 }, /*  89:  0.8227° */
    {    5729,   13953 }, /*  90:  0.8016° */
    {    5553,   14017 }, /*  91:  0.7805° */
    {    5379,   14081 }, /*  92:  0.7594° */
    {    5206,   14145 }, /*  93:  0.7383° */
    {    5034,   14209 }, /*  94:  0.7172° */
    {    4865,   14273 }, /*  95:  0.6961° */
    {    4696,   14337 }, /*  96:  0.6750° */
    {    4529,   14400 }, /*  97:  0.6539° */
    {    4364,   14464 }, /*  98:  0.6328° */
    {    4200,   14528 }, /*  99:  0.6117° */
    {    4038,   14592 }, /* 100:  0.5906° */
    {    3877,   14656 }, /* 101:  0.5695° */
    {    3717,   14720 }, /* 102:  0.5484° */
    {    3559,   14784 }, /* 103:  0.5273° */
    {    3402,   14848 }, /* 104:  0.5062° */
    {    3246,   14912 }, /* 105:  0.4852° */
    {    3092,   14976 }, /* 106:  0.4641° */
    {    2939,   15040 }, /* 107:  0.4430° */
    {    2787,   15104 }, /* 108:  0.4219° */
    {    2636,   15168 }, /* 109:  0.4008° */
    {    2487,   15232 }, /* 110:  0.3797° */
    {    2339,   15296 }, /* 111:  0.3586° */
    {    2193,   15360 }, /* 112:  0.3375° */
    {    2047,   15424 }, /* 113:  0.3164° */
    {    1903,   15488 }, /* 114:  0.2953° */
    {    1760,   15552 }, /* 115:  0.2742° */
    {    1618,   15616 }, /* 116:  0.2531° */
    {    1477,   15680 }, /* 117:  0.2320° */
    {    1337,   15744 }, /* 118:  0.2109° */
    {    1198,   15808 }, /* 119:  0.1898° */
    {    1061,   15872 }, /* 120:  0.1687° */
    {     925,   15936 }, /* 121:  0.1477° */
    {     789,   16000 }, /* 122:  0.1266° */
    {     655,   16064 }, /* 123:  0.1055° */
    {     522,   16128 }, /* 124:  0.0844° */
    {     390,   16192 }, /* 125:  0.0633° */
    {     259,   16256 }, /* 126:  0.0422° */
    {     129,   16320 }, /* 127:  0.0211° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -128,   16448 }, /* 129: -0.0211° */
    {    -255,   16512 }, /* 130: -0.0422° */
    {    -381,   16576 }, /* 131: -0.0633° */
    {    -506,   16640 }, /* 132: -0.0844° */
    {    -630,   16704 }, /* 133: -0.1055° */
    {    -753,   16768 }, /* 134: -0.1266° */
    {    -876,   16832 }, /* 135: -0.1477° */
    {    -997,   16896 }, /* 136: -0.1688° */
    {   -1118,   16960 }, /* 137: -0.1898° */
    {   -1237,   17024 }, /* 138: -0.2109° */
    {   -1356,   17088 }, /* 139: -0.2320° */
    {   -1474,   17152 }, /* 140: -0.2531° */
    {   -1591,   17216 }, /* 141: -0.2742° */
    {   -1707,   17280 }, /* 142: -0.2953° */
    {   -1822,   17344 }, /* 143: -0.3164° */
    {   -1937,   17408 }, /* 144: -0.3375° */
    {   -2050,   17472 }, /* 145: -0.3586° */
    {   -2163,   17536 }, /* 146: -0.3797° */
    {   -2275,   17600 }, /* 147: -0.4008° */
    {   -2386,   17664 }, /* 148: -0.4219° */
    {   -2497,   17728 }, /* 149: -0.4430° */
    {   -2606,   17792 }, /* 150: -0.4641° */
    {   -2715,   17856 }, /* 151: -0.4852° */
    {   -2823,   17920 }, /* 152: -0.5063° */
    {   -2930,   17984 }, /* 153: -0.5273° */
    {   -3037,   18048 }, /* 154: -0.5484° */
    {   -3143,   18112 }, /* 155: -0.5695° */
    {   -3248,   18176 }, /* 156: -0.5906° */
    {   -3352,   18241 }, /* 157: -0.6117° */
    {   -3456,   18305 }, /* 158: -0.6328° */
    {   -3559,   18369 }, /* 159: -0.6539° */
    {   -3661,   18433 }, /* 160: -0.6750° */
    {   -3762,   18497 }, /* 161: -0.6961° */
    {   -3863,   18561 }, /* 162: -0.7172° */
    {   -3963,   18625 }, /* 163: -0.7383° */
    {   -4063,   18689 }, /* 164: -0.7594° */
    {   -4161,   18753 }, /* 165: -0.7805° */
    {   -4259,   18817 }, /* 166: -0.8016° */
    {   -4357,   18881 }, /* 167: -0.8227° */
    {   -4454,   18946 }, /* 168: -0.8438° */
    {   -4550,   19010 }, /* 169: -0.8648° */
    {   -4645,   19074 }, /* 170: -0.8859° */
    {   -4740,   19138 }, /* 171: -0.9070° */
    {   -4834,   19202 }, /* 172: -0.9281° */
    {   -4928,   19266 }, /* 173: -0.9492° */
    {   -5021,   19330 }, /* 174: -0.9703° */
    {   -5113,   19394 }, /* 175: -0.9914° */
    {   -5205,   19459 }, /* 176: -1.0125° */
    {   -5296,   19523 }, /* 177: -1.0336° */
    {   -5387,   19587 }, /* 178: -1.0547° */
    {   -5477,   19651 }, /* 179: -1.0758° */
    {   -5567,   19715 }, /* 180: -1.0969° */
    {   -5655,   19779 }, /* 181: -1.1180° */
    {   -5744,   19843 }, /* 182: -1.1391° */
    {   -5832,   19908 }, /* 183: -1.1602° */
    {   -5919,   19972 }, /* 184: -1.1813° */
    {   -6005,   20036 }, /* 185: -1.2023° */
    {   -6092,   20100 }, /* 186: -1.2234° */
    {   -6177,   20164 }, /* 187: -1.2445° */
    {   -6262,   20228 }, /* 188: -1.2656° */
    {   -6347,   20293 }, /* 189: -1.2867° */
    {   -6431,   20357 }, /* 190: -1.3078° */
    {   -6514,   20421 }, /* 191: -1.3289° */
    {   -6597,   20485 }, /* 192: -1.3500° */
    {   -6680,   20549 }, /* 193: -1.3711° */
    {   -6761,   20614 }, /* 194: -1.3922° */
    {   -6843,   20678 }, /* 195: -1.4133° */
    {   -6924,   20742 }, /* 196: -1.4344° */
    {   -7004,   20806 }, /* 197: -1.4555° */
    {   -7084,   20871 }, /* 198: -1.4766° */
    {   -7164,   20935 }, /* 199: -1.4977° */
    {   -7243,   20999 }, /* 200: -1.5188° */
    {   -7321,   21063 }, /* 201: -1.5398° */
    {   -7399,   21127 }, /* 202: -1.5609° */
    {   -7477,   21192 }, /* 203: -1.5820° */
    {   -7554,   21256 }, /* 204: -1.6031° */
    {   -7631,   21320 }, /* 205: -1.6242° */
    {   -7707,   21384 }, /* 206: -1.6453° */
    {   -7783,   21449 }, /* 207: -1.6664° */
    {   -7858,   21513 }, /* 208: -1.6875° */
    {   -7933,   21577 }, /* 209: -1.7086° */
    {   -8008,   21641 }, /* 210: -1.7297° */
    {   -8082,   21706 }, /* 211: -1.7508° */
    {   -8155,   21770 }, /* 212: -1.7719° */
    {   -8228,   21834 }, /* 213: -1.7930° */
    {   -8301,   21899 }, /* 214: -1.8141° */
    {   -8374,   21963 }, /* 215: -1.8352° */
    {   -8445,   22027 }, /* 216: -1.8563° */
    {   -8517,   22092 }, /* 217: -1.8773° */
    {   -8588,   22156 }, /* 218: -1.8984° */
    {   -8659,   22220 }, /* 219: -1.9195° */
    {   -8729,   22284 }, /* 220: -1.9406° */
    {   -8799,   22349 }, /* 221: -1.9617° */
    {   -8869,   22413 }, /* 222: -1.9828° */
    {   -8938,   22477 }, /* 223: -2.0039° */
    {   -9006,   22542 }, /* 224: -2.0250° */
    {   -9075,   22606 }, /* 225: -2.0461° */
    {   -9143,   22670 }, /* 226: -2.0672° */
    {   -9210,   22735 }, /* 227: -2.0883° */
    {   -9278,   22799 }, /* 228: -2.1094° */
    {   -9344,   22864 }, /* 229: -2.1305° */
    {   -9411,   22928 }, /* 230: -2.1516° */
    {   -9477,   22992 }, /* 231: -2.1727° */
    {   -9543,   23057 }, /* 232: -2.1938° */
    {   -9608,   23121 }, /* 233: -2.2148° */
    {   -9673,   23185 }, /* 234: -2.2359° */
    {   -9738,   23250 }, /* 235: -2.2570° */
    {   -9802,   23314 }, /* 236: -2.2781° */
    {   -9866,   23379 }, /* 237: -2.2992° */
    {   -9930,   23443 }, /* 238: -2.3203° */
    {   -9993,   23507 }, /* 239: -2.3414° */
    {  -10056,   23572 }, /* 240: -2.3625° */
    {  -10119,   23636 }, /* 241: -2.3836° */
    {  -10181,   23701 }, /* 242: -2.4047° */
    {  -10243,   23765 }, /* 243: -2.4258° */
    {  -10305,   23830 }, /* 244: -2.4469° */
    {  -10366,   23894 }, /* 245: -2.4680° */
    {  -10427,   23958 }, /* 246: -2.4891° */
    {  -10488,   24023 }, /* 247: -2.5102° */
    {  -10548,   24087 }, /* 248: -2.5313° */
    {  -10609,   24152 }, /* 249: -2.5523° */
    {  -10668,   24216 }, /* 250: -2.5734° */
    {  -10728,   24281 }, /* 251: -2.5945° */
    {  -10787,   24345 }, /* 252: -2.6156° */
    {  -10846,   24410 }, /* 253: -2.6367° */
    {  -10904,   24474 }, /* 254: -2.6578° */
    {  -10962,   24539 }  /* 255: -2.6789 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 58594 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 5859 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8194 }, /*   0:  1.5360° */
    {   32260,    8258 }, /*   1:  1.5240° */
    {   31760,    8322 }, /*   2:  1.5120° */
    {   31268,    8386 }, /*   3:  1.5000° */
    {   30783,    8450 }, /*   4:  1.4880° */
    {   30305,    8514 }, /*   5:  1.4760° */
    {   29835,    8578 }, /*   6:  1.4640° */
    {   29372,    8642 }, /*   7:  1.4520° */
    {   28915,    8706 }, /*   8:  1.4400° */
    {   28465,    8770 }, /*   9:  1.4280° */
    {   28022,    8834 }, /*  10:  1.4160° */
    {   27585,    8898 }, /*  11:  1.4040° */
    {   27154,    8962 }, /*  12:  1.3920° */
    {   26729,    9026 }, /*  13:  1.3800° */
    {   26310,    9090 }, /*  14:  1.3680° */
    {   25897,    9154 }, /*  15:  1.3560° */
    {   25490,    9218 }, /*  16:  1.3440° */
    {   25089,    9282 }, /*  17:  1.3320° */
    {   24693,    9346 }, /*  18:  1.3200° */
    {   24302,    9410 }, /*  19:  1.3080° */
    {   23917,    9474 }, /*  20:  1.2960° */
    {   23536,    9538 }, /*  21:  1.2840° */
    {   23161,    9602 }, /*  22:  1.2720° */
    {   22791,    9666 }, /*  23:  1.2600° */
    {   22426,    9730 }, /*  24:  1.2480° */
    {   22065,    9794 }, /*  25:  1.2360° */
    {   21709,    9858 }, /*  26:  1.2240° */
    {   21358,    9922 }, /*  27:  1.2120° */
    {   21011,    9986 }, /*  28:  1.2000° */
    {   20669,   10050 }, /*  29:  1.1880° */
    {   20331,   10114 }, /*  30:  1.1760° */
    {   19997,   10178 }, /*  31:  1.1640° */
    {   19667,   10242 }, /*  32:  1.1520° */
    {   19342,   10306 }, /*  33:  1.1400° */
    {   19020,   10370 }, /*  34:  1.1280° */
    {   18703,   10433 }, /*  35:  1.1160° */
    {   18389,   10497 }, /*  36:  1.1040° */
    {   18079,   10561 }, /*  37:  1.0920° */
    {   17773,   10625 }, /*  38:  1.0800° */
    {   17470,   10689 }, /*  39:  1.0680° */
    {   17171,   10753 }, /*  40:  1.0560° */
    {   16876,   10817 }, /*  41:  1.0440° */
    {   16584,   10881 }, /*  42:  1.0320° */
    {   16296,   10945 }, /*  43:  1.0200° */
    {   16010,   11009 }, /*  44:  1.0080° */
    {   15729,   11073 }, /*  45:  0.9960° */
    {   15450,   11137 }, /*  46:  0.9840° */
    {   15174,   11201 }, /*  47:  0.9720° */
    {   14902,   11265 }, /*  48:  0.9600° */
    {   14633,   11329 }, /*  49:  0.9480° */
    {   14367,   11393 }, /*  50:  0.9360° */
    {   14103,   11457 }, /*  51:  0.9240° */
    {   13843,   11521 }, /*  52:  0.9120° */
    {   13586,   11585 }, /*  53:  0.9000° */
    {   13331,   11649 }, /*  54:  0.8880° */
    {   13079,   11713 }, /*  55:  0.8760° */
    {   12830,   11777 }, /*  56:  0.8640° */
    {   12583,   11841 }, /*  57:  0.8520° */
    {   12340,   11905 }, /*  58:  0.8400° */
    {   12098,   11969 }, /*  59:  0.8280° */
    {   11860,   12033 }, /*  60:  0.8160° */
    {   11624,   12097 }, /*  61:  0.8040° */
    {   11390,   12161 }, /*  62:  0.7920° */
    {   11159,   12225 }, /*  63:  0.7800° */
    {   10930,   12289 }, /*  64:  0.7680° */
    {   10704,   12353 }, /*  65:  0.7560° */
    {   10480,   12417 }, /*  66:  0.7440° */
    {   10258,   12481 }, /*  67:  0.7320° */
    {   10038,   12544 }, /*  68:  0.7200° */
    {    9821,   12608 }, /*  69:  0.7080° */
    {    9606,   12672 }, /*  70:  0.6960° */
    {    9393,   12736 }, /*  71:  0.6840° */
    {    9182,   12800 }, /*  72:  0.6720° */
    {    8973,   12864 }, /*  73:  0.6600° */
    {    8767,   12928 }, /*  74:  0.6480° */
    {    8562,   12992 }, /*  75:  0.6360° */
    {    8359,   13056 }, /*  76:  0.6240° */
    {    8159,   13120 }, /*  77:  0.6120° */
    {    7960,   13184 }, /*  78:  0.6000° */
    {    7763,   13248 }, /*  79:  0.5880° */
    {    7568,   13312 }, /*  80:  0.5760° */
    {    7375,   13376 }, /*  81:  0.5640° */
    {    7184,   13440 }, /*  82:  0.5520° */
    {    6994,   13504 }, /*  83:  0.5400° */
    {    6807,   13568 }, /*  84:  0.5280° */
    {    6621,   13632 }, /*  85:  0.5160° */
    {    6437,   13696 }, /*  86:  0.5040° */
    {    6254,   13760 }, /*  87:  0.4920° */
    {    6074,   13824 }, /*  88:  0.4800° */
    {    5895,   13888 }, /*  89:  0.4680° */
    {    5717,   13952 }, /*  90:  0.4560° */
    {    5541,   14016 }, /*  91:  0.4440° */
    {    5367,   14080 }, /*  92:  0.4320° */
    {    5194,   14144 }, /*  93:  0.4200° */
    {    5023,   14208 }, /*  94:  0.4080° */
    {    4854,   14272 }, /*  95:  0.3960° */
    {    4686,   14336 }, /*  96:  0.3840° */
    {    4519,   14400 }, /*  97:  0.3720° */
    {    4354,   14464 }, /*  98:  0.3600° */
    {    4190,   14528 }, /*  99:  0.3480° */
    {    4028,   14592 }, /* 100:  0.3360° */
    {    3867,   14656 }, /* 101:  0.3240° */
    {    3708,   14720 }, /* 102:  0.3120° */
    {    3550,   14784 }, /* 103:  0.3000° */
    {    3393,   14848 }, /* 104:  0.2880° */
    {    3238,   14912 }, /* 105:  0.2760° */
    {    3084,   14976 }, /* 106:  0.2640° */
    {    2931,   15040 }, /* 107:  0.2520° */
    {    2780,   15104 }, /* 108:  0.2400° */
    {    2630,   15168 }, /* 109:  0.2280° */
    {    2481,   15232 }, /* 110:  0.2160° */
    {    2333,   15296 }, /* 111:  0.2040° */
    {    2187,   15360 }, /* 112:  0.1920° */
    {    2042,   15424 }, /* 113:  0.1800° */
    {    1898,   15488 }, /* 114:  0.1680° */
    {    1755,   15552 }, /* 115:  0.1560° */
    {    1613,   15616 }, /* 116:  0.1440° */
    {    1473,   15680 }, /* 117:  0.1320° */
    {    1333,   15744 }, /* 118:  0.1200° */
    {    1195,   15808 }, /* 119:  0.1080° */
    {    1058,   15872 }, /* 120:  0.0960° */
    {     922,   15936 }, /* 121:  0.0840° */
    {     787,   16000 }, /* 122:  0.0720° */
    {     653,   16064 }, /* 123:  0.0600° */
    {     520,   16127 }, /* 124:  0.0480° */
    {     389,   16191 }, /* 125:  0.0360° */
    {     258,   16255 }, /* 126:  0.0240° */
    {     128,   16319 }, /* 127:  0.0120° */
    {       0,   16383 }, /* 128: -0.0000° */
    {    -127,   16447 }, /* 129: -0.0120° */
    {    -254,   16511 }, /* 130: -0.0240° */
    {    -380,   16575 }, /* 131: -0.0360° */
    {    -504,   16639 }, /* 132: -0.0480° */
    {    -628,   16704 }, /* 133: -0.0600° */
    {    -751,   16768 }, /* 134: -0.0720° */
    {    -873,   16832 }, /* 135: -0.0840° */
    {    -994,   16896 }, /* 136: -0.0960° */
    {   -1114,   16960 }, /* 137: -0.1080° */
    {   -1233,   17024 }, /* 138: -0.1200° */
    {   -1352,   17088 }, /* 139: -0.1320° */
    {   -1469,   17152 }, /* 140: -0.1440° */
    {   -1586,   17216 }, /* 141: -0.1560° */
    {   -1701,   17280 }, /* 142: -0.1680° */
    {   -1816,   17344 }, /* 143: -0.1800° */
    {   -1930,   17408 }, /* 144: -0.1920° */
    {   -2043,   17472 }, /* 145: -0.2040° */
    {   -2156,   17536 }, /* 146: -0.2160° */
    {   -2267,   17600 }, /* 147: -0.2280° */
    {   -2378,   17664 }, /* 148: -0.2400° */
    {   -2488,   17728 }, /* 149: -0.2520° */
    {   -2597,   17792 }, /* 150: -0.2640° */
    {   -2705,   17856 }, /* 151: -0.2760° */
    {   -2813,   17920 }, /* 152: -0.2880° */
    {   -2920,   17984 }, /* 153: -0.3000° */
    {   -3026,   18048 }, /* 154: -0.3120° */
    {   -3131,   18112 }, /* 155: -0.3240° */
    {   -3236,   18176 }, /* 156: -0.3360° */
    {   -3340,   18240 }, /* 157: -0.3480° */
    {   -3443,   18304 }, /* 158: -0.3600° */
    {   -3545,   18368 }, /* 159: -0.3720° */
    {   -3647,   18432 }, /* 160: -0.3840° */
    {   -3748,   18496 }, /* 161: -0.3960° */
    {   -3848,   18560 }, /* 162: -0.4080° */
    {   -3948,   18624 }, /* 163: -0.4200° */
    {   -4047,   18688 }, /* 164: -0.4320° */
    {   -4145,   18752 }, /* 165: -0.4440° */
    {   -4243,   18816 }, /* 166: -0.4560° */
    {   -4340,   18880 }, /* 167: -0.4680° */
    {   -4436,   18944 }, /* 168: -0.4800° */
    {   -4532,   19008 }, /* 169: -0.4920° */
    {   -4627,   19072 }, /* 170: -0.5040° */
    {   -4721,   19136 }, /* 171: -0.5160° */
    {   -4815,   19200 }, /* 172: -0.5280° */
    {   -4908,   19264 }, /* 173: -0.5400° */
    {   -5000,   19328 }, /* 174: -0.5520° */
    {   -5092,   19392 }, /* 175: -0.5640° */
    {   -5184,   19456 }, /* 176: -0.5760° */
    {   -5274,   19521 }, /* 177: -0.5880° */
    {   -5364,   19585 }, /* 178: -0.6000° */
    {   -5454,   19649 }, /* 179: -0.6120° */
    {   -5543,   19713 }, /* 180: -0.6240° */
    {   -5631,   19777 }, /* 181: -0.6360° */
    {   -5719,   19841 }, /* 182: -0.6480° */
    {   -5806,   19905 }, /* 183: -0.6600° */
    {   -5893,   19969 }, /* 184: -0.6720° */
    {   -5979,   20033 }, /* 185: -0.6840° */
    {   -6065,   20097 }, /* 186: -0.6960° */
    {   -6150,   20161 }, /* 187: -0.7080° */
    {   -6234,   20225 }, /* 188: -0.7200° */
    {   -6318,   20289 }, /* 189: -0.7320° */
    {   -6402,   20353 }, /* 190: -0.7440° */
    {   -6485,   20417 }, /* 191: -0.7560° */
    {   -6567,   20481 }, /* 192: -0.7680° */
    {   -6649,   20545 }, /* 193: -0.7800° */
    {   -6731,   20609 }, /* 194: -0.7920° */
    {   -6811,   20674 }, /* 195: -0.8040° */
    {   -6892,   20738 }, /* 196: -0.8160° */
    {   -6972,   20802 }, /* 197: -0.8280° */
    {   -7051,   20866 }, /* 198: -0.8400° */
    {   -7130,   20930 }, /* 199: -0.8520° */
    {   -7209,   20994 }, /* 200: -0.8640° */
    {   -7287,   21058 }, /* 201: -0.8760° */
    {   -7364,   21122 }, /* 202: -0.8880° */
    {   -7441,   21186 }, /* 203: -0.9000° */
    {   -7518,   21250 }, /* 204: -0.9120° */
    {   -7594,   21314 }, /* 205: -0.9240° */
    {   -7670,   21378 }, /* 206: -0.9360° */
    {   -7745,   21442 }, /* 207: -0.9480° */
    {   -7820,   21507 }, /* 208: -0.9600° */
    {   -7894,   21571 }, /* 209: -0.9720° */
    {   -7968,   21635 }, /* 210: -0.9840° */
    {   -8041,   21699 }, /* 211: -0.9960° */
    {   -8114,   21763 }, /* 212: -1.0080° */
    {   -8187,   21827 }, /* 213: -1.0200° */
    {   -8259,   21891 }, /* 214: -1.0320° */
    {   -8331,   21955 }, /* 215: -1.0440° */
    {   -8402,   22019 }, /* 216: -1.0560° */
    {   -8473,   22083 }, /* 217: -1.0680° */
    {   -8544,   22147 }, /* 218: -1.0800° */
    {   -8614,   22212 }, /* 219: -1.0920° */
    {   -8684,   22276 }, /* 220: -1.1040° */
    {   -8753,   22340 }, /* 221: -1.1160° */
    {   -8822,   22404 }, /* 222: -1.1280° */
    {   -8891,   22468 }, /* 223: -1.1400° */
    {   -8959,   22532 }, /* 224: -1.1520° */
    {   -9027,   22596 }, /* 225: -1.1640° */
    {   -9094,   22660 }, /* 226: -1.1760° */
    {   -9161,   22724 }, /* 227: -1.1880° */
    {   -9228,   22789 }, /* 228: -1.2000° */
    {   -9294,   22853 }, /* 229: -1.2120° */
    {   -9360,   22917 }, /* 230: -1.2240° */
    {   -9425,   22981 }, /* 231: -1.2360° */
    {   -9491,   23045 }, /* 232: -1.2480° */
    {   -9555,   23109 }, /* 233: -1.2600° */
    {   -9620,   23173 }, /* 234: -1.2720° */
    {   -9684,   23237 }, /* 235: -1.2840° */
    {   -9748,   23301 }, /* 236: -1.2960° */
    {   -9811,   23366 }, /* 237: -1.3080° */
    {   -9874,   23430 }, /* 238: -1.3200° */
    {   -9937,   23494 }, /* 239: -1.3320° */
    {   -9999,   23558 }, /* 240: -1.3440° */
    {  -10061,   23622 }, /* 241: -1.3560° */
    {  -10123,   23686 }, /* 242: -1.3680° */
    {  -10185,   23750 }, /* 243: -1.3800° */
    {  -10246,   23815 }, /* 244: -1.3920° */
    {  -10306,   23879 }, /* 245: -1.4040° */
    {  -10367,   23943 }, /* 246: -1.4160° */
    {  -10427,   24007 }, /* 247: -1.4280° */
    {  -10487,   24071 }, /* 248: -1.4400° */
    {  -10546,   24135 }, /* 249: -1.4520° */
    {  -10605,   24199 }, /* 250: -1.4640° */
    {  -10664,   24264 }, /* 251: -1.4760° */
    {  -10723,   24328 }, /* 252: -1.4880° */
    {  -10781,   24392 }, /* 253: -1.5000° */
    {  -10839,   24456 }, /* 254: -1.5120° */
    {  -10896,   24520 }  /* 255: -1.5240 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 58594 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 5859 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8196 }, /*   0:  1.8432° */
    {   32260,    8260 }, /*   1:  1.8288° */
    {   31760,    8324 }, /*   2:  1.8144° */
    {   31268,    8388 }, /*   3:  1.8000° */
    {   30784,    8452 }, /*   4:  1.7856° */
    {   30306,    8516 }, /*   5:  1.7712° */
    {   29836,    8580 }, /*   6:  1.7568° */
    {   29373,    8643 }, /*   7:  1.7424° */
    {   28916,    8707 }, /*   8:  1.7280° */
    {   28466,    8771 }, /*   9:  1.7136° */
    {   28023,    8835 }, /*  10:  1.6992° */
    {   27586,    8899 }, /*  11:  1.6848° */
    {   27155,    8963 }, /*  12:  1.6704° */
    {   26731,    9027 }, /*  13:  1.6560° */
    {   26312,    9091 }, /*  14:  1.6416° */
    {   25899,    9155 }, /*  15:  1.6272° */
    {   25492,    9219 }, /*  16:  1.6128° */
    {   25091,    9283 }, /*  17:  1.5984° */
    {   24695,    9347 }, /*  18:  1.5840° */
    {   24304,    9411 }, /*  19:  1.5696° */
    {   23919,    9475 }, /*  20:  1.5552° */
    {   23539,    9539 }, /*  21:  1.5408° */
    {   23164,    9603 }, /*  22:  1.5264° */
    {   22794,    9667 }, /*  23:  1.5120° */
    {   22428,    9731 }, /*  24:  1.4976° */
    {   22068,    9795 }, /*  25:  1.4832° */
    {   21712,    9859 }, /*  26:  1.4688° */
    {   21361,    9923 }, /*  27:  1.4544° */
    {   21014,    9987 }, /*  28:  1.4400° */
    {   20672,   10051 }, /*  29:  1.4256° */
    {   20334,   10115 }, /*  30:  1.4112° */
    {   20000,   10179 }, /*  31:  1.3968° */
    {   19670,   10242 }, /*  32:  1.3824° */
    {   19345,   10306 }, /*  33:  1.3680° */
    {   19023,   10370 }, /*  34:  1.3536° */
    {   18706,   10434 }, /*  35:  1.3392° */
    {   18392,   10498 }, /*  36:  1.3248° */
    {   18082,   10562 }, /*  37:  1.3104° */
    {   17776,   10626 }, /*  38:  1.2960° */
    {   17474,   10690 }, /*  39:  1.2816° */
    {   17175,   10754 }, /*  40:  1.2672° */
    {   16879,   10818 }, /*  41:  1.2528° */
    {   16588,   10882 }, /*  42:  1.2384° */
    {   16299,   10946 }, /*  43:  1.2240° */
    {   16014,   11010 }, /*  44:  1.2096° */
    {   15732,   11074 }, /*  45:  1.1952° */
    {   15453,   11138 }, /*  46:  1.1808° */
    {   15178,   11202 }, /*  47:  1.1664° */
    {   14906,   11266 }, /*  48:  1.1520° */
    {   14636,   11330 }, /*  49:  1.1376° */
    {   14370,   11394 }, /*  50:  1.1232° */
    {   14107,   11458 }, /*  51:  1.1088° */
    {   13847,   11522 }, /*  52:  1.0944° */
    {   13589,   11586 }, /*  53:  1.0800° */
    {   13334,   11650 }, /*  54:  1.0656° */
    {   13083,   11713 }, /*  55:  1.0512° */
    {   12833,   11777 }, /*  56:  1.0368° */
    {   12587,   11841 }, /*  57:  1.0224° */
    {   12343,   11905 }, /*  58:  1.0080° */
    {   12102,   11969 }, /*  59:  0.9936° */
    {   11863,   12033 }, /*  60:  0.9792° */
    {   11627,   12097 }, /*  61:  0.9648° */
    {   11393,   12161 }, /*  62:  0.9504° */
    {   11162,   12225 }, /*  63:  0.9360° */
    {   10933,   12289 }, /*  64:  0.9216° */
    {   10707,   12353 }, /*  65:  0.9072° */
    {   10483,   12417 }, /*  66:  0.8928° */
    {   10261,   12481 }, /*  67:  0.8784° */
    {   10042,   12545 }, /*  68:  0.8640° */
    {    9824,   12609 }, /*  69:  0.8496° */
    {    9609,   12673 }, /*  70:  0.8352° */
    {    9396,   12737 }, /*  71:  0.8208° */
    {    9185,   12801 }, /*  72:  0.8064° */
    {    8976,   12865 }, /*  73:  0.7920° */
    {    8770,   12929 }, /*  74:  0.7776° */
    {    8565,   12993 }, /*  75:  0.7632° */
    {    8362,   13057 }, /*  76:  0.7488° */
    {    8162,   13121 }, /*  77:  0.7344° */
    {    7963,   13185 }, /*  78:  0.7200° */
    {    7766,   13249 }, /*  79:  0.7056° */
    {    7571,   13312 }, /*  80:  0.6912° */
    {    7378,   13376 }, /*  81:  0.6768° */
    {    7187,   13440 }, /*  82:  0.6624° */
    {    6997,   13504 }, /*  83:  0.6480° */
    {    6810,   13568 }, /*  84:  0.6336° */
    {    6624,   13632 }, /*  85:  0.6192° */
    {    6440,   13696 }, /*  86:  0.6048° */
    {    6257,   13760 }, /*  87:  0.5904° */
    {    6076,   13824 }, /*  88:  0.5760° */
    {    5897,   13888 }, /*  89:  0.5616° */
    {    5720,   13952 }, /*  90:  0.5472° */
    {    5544,   14016 }, /*  91:  0.5328° */
    {    5370,   14080 }, /*  92:  0.5184° */
    {    5197,   14144 }, /*  93:  0.5040° */
    {    5026,   14208 }, /*  94:  0.4896° */
    {    4856,   14272 }, /*  95:  0.4752° */
    {    4688,   14336 }, /*  96:  0.4608° */
    {    4521,   14400 }, /*  97:  0.4464° */
    {    4356,   14464 }, /*  98:  0.4320° */
    {    4192,   14528 }, /*  99:  0.4176° */
    {    4030,   14592 }, /* 100:  0.4032° */
    {    3869,   14656 }, /* 101:  0.3888° */
    {    3710,   14720 }, /* 102:  0.3744° */
    {    3552,   14784 }, /* 103:  0.3600° */
    {    3395,   14848 }, /* 104:  0.3456° */
    {    3240,   14912 }, /* 105:  0.3312° */
    {    3086,   14976 }, /* 106:  0.3168° */
    {    2933,   15040 }, /* 107:  0.3024° */
    {    2781,   15104 }, /* 108:  0.2880° */
    {    2631,   15168 }, /* 109:  0.2736° */
    {    2482,   15232 }, /* 110:  0.2592° */
    {    2334,   15296 }, /* 111:  0.2448° */
    {    2188,   15360 }, /* 112:  0.2304° */
    {    2043,   15424 }, /* 113:  0.2160° */
    {    1899,   15488 }, /* 114:  0.2016° */
    {    1756,   15552 }, /* 115:  0.1872° */
    {    1614,   15616 }, /* 116:  0.1728° */
    {    1474,   15680 }, /* 117:  0.1584° */
    {    1334,   15744 }, /* 118:  0.1440° */
    {    1196,   15808 }, /* 119:  0.1296° */
    {    1059,   15872 }, /* 120:  0.1152° */
    {     922,   15936 }, /* 121:  0.1008° */
    {     787,   16000 }, /* 122:  0.0864° */
    {     654,   16064 }, /* 123:  0.0720° */
    {     521,   16128 }, /* 124:  0.0576° */
    {     389,   16192 }, /* 125:  0.0432° */
    {     258,   16256 }, /* 126:  0.0288° */
    {     128,   16320 }, /* 127:  0.0144° */
    {       0,   16384 }, /* 128:  0.0000° */
    {    -127,   16448 }, /* 129: -0.0144° */
    {    -254,   16512 }, /* 130: -0.0288° */
    {    -380,   16576 }, /* 131: -0.0432° */
    {    -505,   16640 }, /* 132: -0.0576° */
    {    -629,   16704 }, /* 133: -0.0720° */
    {    -752,   16768 }, /* 134: -0.0864° */
    {    -874,   16832 }, /* 135: -0.1008° */
    {    -995,   16896 }, /* 136: -0.1152° */
    {   -1115,   16960 }, /* 137: -0.1296° */
    {   -1234,   17024 }, /* 138: -0.1440° */
    {   -1353,   17088 }, /* 139: -0.1584° */
    {   -1470,   17152 }, /* 140: -0.1728° */
    {   -1587,   17216 }, /* 141: -0.1872° */
    {   -1702,   17280 }, /* 142: -0.2016° */
    {   -1817,   17344 }, /* 143: -0.2160° */
    {   -1932,   17408 }, /* 144: -0.2304° */
    {   -2045,   17472 }, /* 145: -0.2448° */
    {   -2157,   17536 }, /* 146: -0.2592° */
    {   -2269,   17600 }, /* 147: -0.2736° */
    {   -2380,   17664 }, /* 148: -0.2880° */
    {   -2490,   17728 }, /* 149: -0.3024° */
    {   -2599,   17792 }, /* 150: -0.3168° */
    {   -2707,   17856 }, /* 151: -0.3312° */
    {   -2815,   17920 }, /* 152: -0.3456° */
    {   -2922,   17984 }, /* 153: -0.3600° */
    {   -3028,   18048 }, /* 154: -0.3744° */
    {   -3134,   18112 }, /* 155: -0.3888° */
    {   -3238,   18176 }, /* 156: -0.4032° */
    {   -3342,   18240 }, /* 157: -0.4176° */
    {   -3446,   18304 }, /* 158: -0.4320° */
    {   -3548,   18368 }, /* 159: -0.4464° */
    {   -3650,   18432 }, /* 160: -0.4608° */
    {   -3751,   18496 }, /* 161: -0.4752° */
    {   -3851,   18560 }, /* 162: -0.4896° */
    {   -3951,   18624 }, /* 163: -0.5040° */
    {   -4050,   18688 }, /* 164: -0.5184° */
    {   -4149,   18752 }, /* 165: -0.5328° */
    {   -4246,   18816 }, /* 166: -0.5472° */
    {   -4343,   18880 }, /* 167: -0.5616° */
    {   -4440,   18944 }, /* 168: -0.5760° */
    {   -4535,   19009 }, /* 169: -0.5904° */
    {   -4631,   19073 }, /* 170: -0.6048° */
    {   -4725,   19137 }, /* 171: -0.6192° */
    {   -4819,   19201 }, /* 172: -0.6336° */
    {   -4912,   19265 }, /* 173: -0.6480° */
    {   -5005,   19329 }, /* 174: -0.6624° */
    {   -5097,   19393 }, /* 175: -0.6768° */
    {   -5188,   19457 }, /* 176: -0.6912° */
    {   -5279,   19521 }, /* 177: -0.7056° */
    {   -5369,   19585 }, /* 178: -0.7200° */
    {   -5459,   19649 }, /* 179: -0.7344° */
    {   -5548,   19713 }, /* 180: -0.7488° */
    {   -5636,   19777 }, /* 181: -0.7632° */
    {   -5724,   19841 }, /* 182: -0.7776° */
    {   -5812,   19905 }, /* 183: -0.7920° */
    {   -5898,   19969 }, /* 184: -0.8064° */
    {   -5985,   20034 }, /* 185: -0.8208° */
    {   -6070,   20098 }, /* 186: -0.8352° */
    {   -6156,   20162 }, /* 187: -0.8496° */
    {   -6240,   20226 }, /* 188: -0.8640° */
    {   -6324,   20290 }, /* 189: -0.8784° */
    {   -6408,   20354 }, /* 190: -0.8928° */
    {   -6491,   20418 }, /* 191: -0.9072° */
    {   -6574,   20482 }, /* 192: -0.9216° */
    {   -6656,   20546 }, /* 193: -0.9360° */
    {   -6737,   20610 }, /* 194: -0.9504° */
    {   -6818,   20674 }, /* 195: -0.9648° */
    {   -6899,   20739 }, /* 196: -0.9792° */
    {   -6979,   20803 }, /* 197: -0.9936° */
    {   -7058,   20867 }, /* 198: -1.0080° */
    {   -7137,   20931 }, /* 199: -1.0224° */
    {   -7216,   20995 }, /* 200: -1.0368° */
    {   -7294,   21059 }, /* 201: -1.0512° */
    {   -7372,   21123 }, /* 202: -1.0656° */
    {   -7449,   21187 }, /* 203: -1.0800° */
    {   -7525,   21251 }, /* 204: -1.0944° */
    {   -7602,   21316 }, /* 205: -1.1088° */
    {   -7678,   21380 }, /* 206: -1.1232° */
    {   -7753,   21444 }, /* 207: -1.1376° */
    {   -7828,   21508 }, /* 208: -1.1520° */
    {   -7902,   21572 }, /* 209: -1.1664° */
    {   -7976,   21636 }, /* 210: -1.1808° */
    {   -8050,   21700 }, /* 211: -1.1952° */
    {   -8123,   21764 }, /* 212: -1.2096° */
    {   -8196,   21829 }, /* 213: -1.2240° */
    {   -8268,   21893 }, /* 214: -1.2384° */
    {   -8340,   21957 }, /* 215: -1.2528° */
    {   -8411,   22021 }, /* 216: -1.2672° */
    {   -8482,   22085 }, /* 217: -1.2816° */
    {   -8553,   22149 }, /* 218: -1.2960° */
    {   -8623,   22213 }, /* 219: -1.3104° */
    {   -8693,   22277 }, /* 220: -1.3248° */
    {   -8763,   22342 }, /* 221: -1.3392° */
    {   -8832,   22406 }, /* 222: -1.3536° */
    {   -8900,   22470 }, /* 223: -1.3680° */
    {   -8969,   22534 }, /* 224: -1.3824° */
    {   -9037,   22598 }, /* 225: -1.3968° */
    {   -9104,   22662 }, /* 226: -1.4112° */
    {   -9171,   22727 }, /* 227: -1.4256° */
    {   -9238,   22791 }, /* 228: -1.4400° */
    {   -9304,   22855 }, /* 229: -1.4544° */
    {   -9371,   22919 }, /* 230: -1.4688° */
    {   -9436,   22983 }, /* 231: -1.4832° */
    {   -9502,   23047 }, /* 232: -1.4976° */
    {   -9566,   23112 }, /* 233: -1.5120° */
    {   -9631,   23176 }, /* 234: -1.5264° */
    {   -9695,   23240 }, /* 235: -1.5408° */
    {   -9759,   23304 }, /* 236: -1.5552° */
    {   -9823,   23368 }, /* 237: -1.5696° */
    {   -9886,   23433 }, /* 238: -1.5840° */
    {   -9949,   23497 }, /* 239: -1.5984° */
    {  -10011,   23561 }, /* 240: -1.6128° */
    {  -10073,   23625 }, /* 241: -1.6272° */
    {  -10135,   23689 }, /* 242: -1.6416° */
    {  -10197,   23753 }, /* 243: -1.6560° */
    {  -10258,   23818 }, /* 244: -1.6704° */
    {  -10319,   23882 }, /* 245: -1.6848° */
    {  -10379,   23946 }, /* 246: -1.6992° */
    {  -10440,   24010 }, /* 247: -1.7136° */
    {  -10500,   24075 }, /* 248: -1.7280° */
    {  -10559,   24139 }, /* 249: -1.7424° */
    {  -10618,   24203 }, /* 250: -1.7568° */
    {  -10677,   24267 }, /* 251: -1.7712° */
    {  -10736,   24331 }, /* 252: -1.7856° */
    {  -10794,   24396 }, /* 253: -1.8000° */
    {  -10852,   24460 }, /* 254: -1.8144° */
    {  -10910,   24524 }  /* 255: -1.8288 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 60000 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 6000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8194 }, /*   0:  1.5000° */
    {   32260,    8258 }, /*   1:  1.4883° */
    {   31760,    8322 }, /*   2:  1.4766° */
    {   31268,    8386 }, /*   3:  1.4648° */
    {   30783,    8450 }, /*   4:  1.4531° */
    {   30305,    8514 }, /*   5:  1.4414° */
    {   29835,    8578 }, /*   6:  1.4297° */
    {   29372,    8642 }, /*   7:  1.4180° */
    {   28915,    8706 }, /*   8:  1.4062° */
    {   28465,    8770 }, /*   9:  1.3945° */
    {   28022,    8834 }, /*  10:  1.3828° */
    {   27584,    8898 }, /*  11:  1.3711° */
    {   27154,    8962 }, /*  12:  1.3594° */
    {   26729,    9026 }, /*  13:  1.3477° */
    {   26310,    9090 }, /*  14:  1.3359° */
    {   25897,    9154 }, /*  15:  1.3242° */
    {   25490,    9218 }, /*  16:  1.3125° */
    {   25089,    9282 }, /*  17:  1.3008° */
    {   24692,    9346 }, /*  18:  1.2891° */
    {   24302,    9410 }, /*  19:  1.2773° */
    {   23916,    9474 }, /*  20:  1.2656° */
    {   23536,    9538 }, /*  21:  1.2539° */
    {   23161,    9602 }, /*  22:  1.2422° */
    {   22791,    9666 }, /*  23:  1.2305° */
    {   22425,    9730 }, /*  24:  1.2187° */
    {   22065,    9794 }, /*  25:  1.2070° */
    {   21709,    9858 }, /*  26:  1.1953° */
    {   21358,    9922 }, /*  27:  1.1836° */
    {   21011,    9986 }, /*  28:  1.1719° */
    {   20669,   10050 }, /*  29:  1.1602° */
    {   20330,   10114 }, /*  30:  1.1484° */
    {   19997,   10178 }, /*  31:  1.1367° */
    {   19667,   10241 }, /*  32:  1.1250° */
    {   19341,   10305 }, /*  33:  1.1133° */
    {   19020,   10369 }, /*  34:  1.1016° */
    {   18702,   10433 }, /*  35:  1.0898° */
    {   18389,   10497 }, /*  36:  1.0781° */
    {   18079,   10561 }, /*  37:  1.0664° */
    {   17772,   10625 }, /*  38:  1.0547° */
    {   17470,   10689 }, /*  39:  1.0430° */
    {   17171,   10753 }, /*  40:  1.0312° */
    {   16876,   10817 }, /*  41:  1.0195° */
    {   16584,   10881 }, /*  42:  1.0078° */
    {   16295,   10945 }, /*  43:  0.9961° */
    {   16010,   11009 }, /*  44:  0.9844° */
    {   15728,   11073 }, /*  45:  0.9727° */
    {   15449,   11137 }, /*  46:  0.9609° */
    {   15174,   11201 }, /*  47:  0.9492° */
    {   14902,   11265 }, /*  48:  0.9375° */
    {   14632,   11329 }, /*  49:  0.9258° */
    {   14366,   11393 }, /*  50:  0.9141° */
    {   14103,   11457 }, /*  51:  0.9023° */
    {   13843,   11521 }, /*  52:  0.8906° */
    {   13585,   11585 }, /*  53:  0.8789° */
    {   13330,   11649 }, /*  54:  0.8672° */
    {   13079,   11713 }, /*  55:  0.8555° */
    {   12829,   11777 }, /*  56:  0.8437° */
    {   12583,   11841 }, /*  57:  0.8320° */
    {   12339,   11905 }, /*  58:  0.8203° */
    {   12098,   11969 }, /*  59:  0.8086° */
    {   11859,   12033 }, /*  60:  0.7969° */
    {   11623,   12097 }, /*  61:  0.7852° */
    {   11390,   12161 }, /*  62:  0.7734° */
    {   11158,   12225 }, /*  63:  0.7617° */
    {   10930,   12289 }, /*  64:  0.7500° */
    {   10703,   12353 }, /*  65:  0.7383° */
    {   10479,   12417 }, /*  66:  0.7266° */
    {   10257,   12480 }, /*  67:  0.7148° */
    {   10038,   12544 }, /*  68:  0.7031° */
    {    9820,   12608 }, /*  69:  0.6914° */
    {    9605,   12672 }, /*  70:  0.6797° */
    {    9392,   12736 }, /*  71:  0.6680° */
    {    9182,   12800 }, /*  72:  0.6562° */
    {    8973,   12864 }, /*  73:  0.6445° */
    {    8766,   12928 }, /*  74:  0.6328° */
    {    8562,   12992 }, /*  75:  0.6211° */
    {    8359,   13056 }, /*  76:  0.6094° */
    {    8158,   13120 }, /*  77:  0.5977° */
    {    7960,   13184 }, /*  78:  0.5859° */
    {    7763,   13248 }, /*  79:  0.5742° */
    {    7568,   13312 }, /*  80:  0.5625° */
    {    7375,   13376 }, /*  81:  0.5508° */
    {    7184,   13440 }, /*  82:  0.5391° */
    {    6994,   13504 }, /*  83:  0.5273° */
    {    6807,   13568 }, /*  84:  0.5156° */
    {    6621,   13632 }, /*  85:  0.5039° */
    {    6437,   13696 }, /*  86:  0.4922° */
    {    6254,   13760 }, /*  87:  0.4805° */
    {    6073,   13824 }, /*  88:  0.4687° */
    {    5894,   13888 }, /*  89:  0.4570° */
    {    5717,   13952 }, /*  90:  0.4453° */
    {    5541,   14016 }, /*  91:  0.4336° */
    {    5367,   14080 }, /*  92:  0.4219° */
    {    5194,   14144 }, /*  93:  0.4102° */
    {    5023,   14208 }, /*  94:  0.3984° */
    {    4853,   14272 }, /*  95:  0.3867° */
    {    4685,   14336 }, /*  96:  0.3750° */
    {    4519,   14400 }, /*  97:  0.3633° */
    {    4354,   14464 }, /*  98:  0.3516° */
    {    4190,   14528 }, /*  99:  0.3398° */
    {    4028,   14592 }, /* 100:  0.3281° */
    {    3867,   14656 }, /* 101:  0.3164° */
    {    3708,   14720 }, /* 102:  0.3047° */
    {    3550,   14784 }, /* 103:  0.2930° */
    {    3393,   14848 }, /* 104:  0.2812° */
    {    3238,   14912 }, /* 105:  0.2695° */
    {    3084,   14976 }, /* 106:  0.2578° */
    {    2931,   15040 }, /* 107:  0.2461° */
    {    2780,   15104 }, /* 108:  0.2344° */
    {    2630,   15168 }, /* 109:  0.2227° */
    {    2481,   15232 }, /* 110:  0.2109° */
    {    2333,   15296 }, /* 111:  0.1992° */
    {    2187,   15360 }, /* 112:  0.1875° */
    {    2041,   15424 }, /* 113:  0.1758° */
    {    1897,   15488 }, /* 114:  0.1641° */
    {    1755,   15552 }, /* 115:  0.1523° */
    {    1613,   15616 }, /* 116:  0.1406° */
    {    1473,   15680 }, /* 117:  0.1289° */
    {    1333,   15744 }, /* 118:  0.1172° */
    {    1195,   15808 }, /* 119:  0.1055° */
    {    1058,   15872 }, /* 120:  0.0937° */
    {     922,   15936 }, /* 121:  0.0820° */
    {     787,   16000 }, /* 122:  0.0703° */
    {     653,   16064 }, /* 123:  0.0586° */
    {     520,   16128 }, /* 124:  0.0469° */
    {     389,   16192 }, /* 125:  0.0352° */
    {     258,   16256 }, /* 126:  0.0234° */
    {     128,   16320 }, /* 127:  0.0117° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0117° */
    {    -254,   16512 }, /* 130: -0.0234° */
    {    -380,   16576 }, /* 131: -0.0352° */
    {    -504,   16640 }, /* 132: -0.0469° */
    {    -628,   16704 }, /* 133: -0.0586° */
    {    -751,   16768 }, /* 134: -0.0703° */
    {    -873,   16832 }, /* 135: -0.0820° */
    {    -994,   16896 }, /* 136: -0.0938° */
    {   -1114,   16960 }, /* 137: -0.1055° */
    {   -1233,   17024 }, /* 138: -0.1172° */
    {   -1352,   17088 }, /* 139: -0.1289° */
    {   -1469,   17152 }, /* 140: -0.1406° */
    {   -1585,   17216 }, /* 141: -0.1523° */
    {   -1701,   17280 }, /* 142: -0.1641° */
    {   -1816,   17344 }, /* 143: -0.1758° */
    {   -1930,   17408 }, /* 144: -0.1875° */
    {   -2043,   17472 }, /* 145: -0.1992° */
    {   -2156,   17536 }, /* 146: -0.2109° */
    {   -2267,   17600 }, /* 147: -0.2227° */
    {   -2378,   17664 }, /* 148: -0.2344° */
    {   -2488,   17728 }, /* 149: -0.2461° */
    {   -2597,   17792 }, /* 150: -0.2578° */
    {   -2705,   17856 }, /* 151: -0.2695° */
    {   -2813,   17920 }, /* 152: -0.2813° */
    {   -2920,   17984 }, /* 153: -0.2930° */
    {   -3026,   18048 }, /* 154: -0.3047° */
    {   -3131,   18112 }, /* 155: -0.3164° */
    {   -3236,   18176 }, /* 156: -0.3281° */
    {   -3339,   18240 }, /* 157: -0.3398° */
    {   -3443,   18304 }, /* 158: -0.3516° */
    {   -3545,   18368 }, /* 159: -0.3633° */
    {   -3647,   18432 }, /* 160: -0.3750° */
    {   -3748,   18496 }, /* 161: -0.3867° */
    {   -3848,   18560 }, /* 162: -0.3984° */
    {   -3948,   18624 }, /* 163: -0.4102° */
    {   -4047,   18688 }, /* 164: -0.4219° */
    {   -4145,   18752 }, /* 165: -0.4336° */
    {   -4242,   18816 }, /* 166: -0.4453° */
    {   -4339,   18880 }, /* 167: -0.4570° */
    {   -4436,   18944 }, /* 168: -0.4688° */
    {   -4531,   19008 }, /* 169: -0.4805° */
    {   -4626,   19072 }, /* 170: -0.4922° */
    {   -4721,   19136 }, /* 171: -0.5039° */
    {   -4814,   19200 }, /* 172: -0.5156° */
    {   -4907,   19264 }, /* 173: -0.5273° */
    {   -5000,   19328 }, /* 174: -0.5391° */
    {   -5092,   19392 }, /* 175: -0.5508° */
    {   -5183,   19456 }, /* 176: -0.5625° */
    {   -5274,   19520 }, /* 177: -0.5742° */
    {   -5364,   19585 }, /* 178: -0.5859° */
    {   -5454,   19649 }, /* 179: -0.5977° */
    {   -5542,   19713 }, /* 180: -0.6094° */
    {   -5631,   19777 }, /* 181: -0.6211° */
    {   -5719,   19841 }, /* 182: -0.6328° */
    {   -5806,   19905 }, /* 183: -0.6445° */
    {   -5893,   19969 }, /* 184: -0.6563° */
    {   -5979,   20033 }, /* 185: -0.6680° */
    {   -6064,   20097 }, /* 186: -0.6797° */
    {   -6149,   20161 }, /* 187: -0.6914° */
    {   -6234,   20225 }, /* 188: -0.7031° */
    {   -6318,   20289 }, /* 189: -0.7148° */
    {   -6401,   20353 }, /* 190: -0.7266° */
    {   -6484,   20417 }, /* 191: -0.7383° */
    {   -6567,   20481 }, /* 192: -0.7500° */
    {   -6649,   20545 }, /* 193: -0.7617° */
    {   -6730,   20609 }, /* 194: -0.7734° */
    {   -6811,   20673 }, /* 195: -0.7852° */
    {   -6891,   20738 }, /* 196: -0.7969° */
    {   -6971,   20802 }, /* 197: -0.8086° */
    {   -7051,   20866 }, /* 198: -0.8203° */
    {   -7129,   20930 }, /* 199: -0.8320° */
    {   -7208,   20994 }, /* 200: -0.8438° */
    {   -7286,   21058 }, /* 201: -0.8555° */
    {   -7363,   21122 }, /* 202: -0.8672° */
    {   -7440,   21186 }, /* 203: -0.8789° */
    {   -7517,   21250 }, /* 204: -0.8906° */
    {   -7593,   21314 }, /* 205: -0.9023° */
    {   -7669,   21378 }, /* 206: -0.9141° */
    {   -7744,   21442 }, /* 207: -0.9258° */
    {   -7819,   21506 }, /* 208: -0.9375° */
    {   -7893,   21570 }, /* 209: -0.9492° */
    {   -7967,   21635 }, /* 210: -0.9609° */
    {   -8041,   21699 }, /* 211: -0.9727° */
    {   -8114,   21763 }, /* 212: -0.9844° */
    {   -8186,   21827 }, /* 213: -0.9961° */
    {   -8258,   21891 }, /* 214: -1.0078° */
    {   -8330,   21955 }, /* 215: -1.0195° */
    {   -8401,   22019 }, /* 216: -1.0313° */
    {   -8472,   22083 }, /* 217: -1.0430° */
    {   -8543,   22147 }, /* 218: -1.0547° */
    {   -8613,   22211 }, /* 219: -1.0664° */
    {   -8683,   22275 }, /* 220: -1.0781° */
    {   -8752,   22340 }, /* 221: -1.0898° */
    {   -8821,   22404 }, /* 222: -1.1016° */
    {   -8890,   22468 }, /* 223: -1.1133° */
    {   -8958,   22532 }, /* 224: -1.1250° */
    {   -9026,   22596 }, /* 225: -1.1367° */
    {   -9093,   22660 }, /* 226: -1.1484° */
    {   -9160,   22724 }, /* 227: -1.1602° */
    {   -9227,   22788 }, /* 228: -1.1719° */
    {   -9293,   22852 }, /* 229: -1.1836° */
    {   -9359,   22917 }, /* 230: -1.1953° */
    {   -9424,   22981 }, /* 231: -1.2070° */
    {   -9489,   23045 }, /* 232: -1.2188° */
    {   -9554,   23109 }, /* 233: -1.2305° */
    {   -9619,   23173 }, /* 234: -1.2422° */
    {   -9683,   23237 }, /* 235: -1.2539° */
    {   -9747,   23301 }, /* 236: -1.2656° */
    {   -9810,   23365 }, /* 237: -1.2773° */
    {   -9873,   23429 }, /* 238: -1.2891° */
    {   -9936,   23494 }, /* 239: -1.3008° */
    {   -9998,   23558 }, /* 240: -1.3125° */
    {  -10060,   23622 }, /* 241: -1.3242° */
    {  -10122,   23686 }, /* 242: -1.3359° */
    {  -10183,   23750 }, /* 243: -1.3477° */
    {  -10244,   23814 }, /* 244: -1.3594° */
    {  -10305,   23878 }, /* 245: -1.3711° */
    {  -10365,   23943 }, /* 246: -1.3828° */
    {  -10425,   24007 }, /* 247: -1.3945° */
    {  -10485,   24071 }, /* 248: -1.4063° */
    {  -10545,   24135 }, /* 249: -1.4180° */
    {  -10604,   24199 }, /* 250: -1.4297° */
    {  -10663,   24263 }, /* 251: -1.4414° */
    {  -10721,   24327 }, /* 252: -1.4531° */
    {  -10779,   24392 }, /* 253: -1.4648° */
    {  -10837,   24456 }, /* 254: -1.4766° */
    {  -10895,   24520 }  /* 255: -1.4883 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 60000 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 6000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8196 }, /*   0:  1.8000° */
    {   32260,    8260 }, /*   1:  1.7859° */
    {   31760,    8323 }, /*   2:  1.7719° */
    {   31268,    8387 }, /*   3:  1.7578° */
    {   30783,    8451 }, /*   4:  1.7438° */
    {   30306,    8515 }, /*   5:  1.7297° */
    {   29836,    8579 }, /*   6:  1.7156° */
    {   29373,    8643 }, /*   7:  1.7016° */
    {   28916,    8707 }, /*   8:  1.6875° */
    {   28466,    8771 }, /*   9:  1.6734° */
    {   28023,    8835 }, /*  10:  1.6594° */
    {   27586,    8899 }, /*  11:  1.6453° */
    {   27155,    8963 }, /*  12:  1.6313° */
    {   26731,    9027 }, /*  13:  1.6172° */
    {   26312,    9091 }, /*  14:  1.6031° */
    {   25899,    9155 }, /*  15:  1.5891° */
    {   25492,    9219 }, /*  16:  1.5750° */
    {   25091,    9283 }, /*  17:  1.5609° */
    {   24695,    9347 }, /*  18:  1.5469° */
    {   24304,    9411 }, /*  19:  1.5328° */
    {   23919,    9475 }, /*  20:  1.5187° */
    {   23539,    9539 }, /*  21:  1.5047° */
    {   23163,    9603 }, /*  22:  1.4906° */
    {   22793,    9667 }, /*  23:  1.4766° */
    {   22428,    9731 }, /*  24:  1.4625° */
    {   22068,    9795 }, /*  25:  1.4484° */
    {   21712,    9859 }, /*  26:  1.4344° */
    {   21360,    9923 }, /*  27:  1.4203° */
    {   21014,    9987 }, /*  28:  1.4062° */
    {   20671,   10050 }, /*  29:  1.3922° */
    {   20333,   10114 }, /*  30:  1.3781° */
    {   20000,   10178 }, /*  31:  1.3641° */
    {   19670,   10242 }, /*  32:  1.3500° */
    {   19344,   10306 }, /*  33:  1.3359° */
    {   19023,   10370 }, /*  34:  1.3219° */
    {   18705,   10434 }, /*  35:  1.3078° */
    {   18392,   10498 }, /*  36:  1.2937° */
    {   18082,   10562 }, /*  37:  1.2797° */
    {   17776,   10626 }, /*  38:  1.2656° */
    {   17473,   10690 }, /*  39:  1.2516° */
    {   17174,   10754 }, /*  40:  1.2375° */
    {   16879,   10818 }, /*  41:  1.2234° */
    {   16587,   10882 }, /*  42:  1.2094° */
    {   16298,   10946 }, /*  43:  1.1953° */
    {   16013,   11010 }, /*  44:  1.1812° */
    {   15731,   11074 }, /*  45:  1.1672° */
    {   15453,   11138 }, /*  46:  1.1531° */
    {   15177,   11202 }, /*  47:  1.1391° */
    {   14905,   11266 }, /*  48:  1.1250° */
    {   14636,   11330 }, /*  49:  1.1109° */
    {   14370,   11394 }, /*  50:  1.0969° */
    {   14106,   11458 }, /*  51:  1.0828° */
    {   13846,   11522 }, /*  52:  1.0687° */
    {   13588,   11585 }, /*  53:  1.0547° */
    {   13334,   11649 }, /*  54:  1.0406° */
    {   13082,   11713 }, /*  55:  1.0266° */
    {   12833,   11777 }, /*  56:  1.0125° */
    {   12586,   11841 }, /*  57:  0.9984° */
    {   12343,   11905 }, /*  58:  0.9844° */
    {   12101,   11969 }, /*  59:  0.9703° */
    {   11863,   12033 }, /*  60:  0.9562° */
    {   11627,   12097 }, /*  61:  0.9422° */
    {   11393,   12161 }, /*  62:  0.9281° */
    {   11162,   12225 }, /*  63:  0.9141° */
    {   10933,   12289 }, /*  64:  0.9000° */
    {   10707,   12353 }, /*  65:  0.8859° */
    {   10482,   12417 }, /*  66:  0.8719° */
    {   10261,   12481 }, /*  67:  0.8578° */
    {   10041,   12545 }, /*  68:  0.8437° */
    {    9824,   12609 }, /*  69:  0.8297° */
    {    9609,   12673 }, /*  70:  0.8156° */
    {    9396,   12737 }, /*  71:  0.8016° */
    {    9185,   12801 }, /*  72:  0.7875° */
    {    8976,   12865 }, /*  73:  0.7734° */
    {    8769,   12929 }, /*  74:  0.7594° */
    {    8565,   12993 }, /*  75:  0.7453° */
    {    8362,   13057 }, /*  76:  0.7312° */
    {    8161,   13121 }, /*  77:  0.7172° */
    {    7962,   13184 }, /*  78:  0.7031° */
    {    7766,   13248 }, /*  79:  0.6891° */
    {    7571,   13312 }, /*  80:  0.6750° */
    {    7378,   13376 }, /*  81:  0.6609° */
    {    7186,   13440 }, /*  82:  0.6469° */
    {    6997,   13504 }, /*  83:  0.6328° */
    {    6809,   13568 }, /*  84:  0.6187° */
    {    6623,   13632 }, /*  85:  0.6047° */
    {    6439,   13696 }, /*  86:  0.5906° */
    {    6257,   13760 }, /*  87:  0.5766° */
    {    6076,   13824 }, /*  88:  0.5625° */
    {    5897,   13888 }, /*  89:  0.5484° */
    {    5719,   13952 }, /*  90:  0.5344° */
    {    5543,   14016 }, /*  91:  0.5203° */
    {    5369,   14080 }, /*  92:  0.5062° */
    {    5196,   14144 }, /*  93:  0.4922° */
    {    5025,   14208 }, /*  94:  0.4781° */
    {    4856,   14272 }, /*  95:  0.4641° */
    {    4688,   14336 }, /*  96:  0.4500° */
    {    4521,   14400 }, /*  97:  0.4359° */
    {    4356,   14464 }, /*  98:  0.4219° */
    {    4192,   14528 }, /*  99:  0.4078° */
    {    4030,   14592 }, /* 100:  0.3937° */
    {    3869,   14656 }, /* 101:  0.3797° */
    {    3710,   14720 }, /* 102:  0.3656° */
    {    3551,   14784 }, /* 103:  0.3516° */
    {    3395,   14848 }, /* 104:  0.3375° */
    {    3239,   14912 }, /* 105:  0.3234° */
    {    3085,   14976 }, /* 106:  0.3094° */
    {    2933,   15040 }, /* 107:  0.2953° */
    {    2781,   15104 }, /* 108:  0.2812° */
    {    2631,   15168 }, /* 109:  0.2672° */
    {    2482,   15232 }, /* 110:  0.2531° */
    {    2334,   15296 }, /* 111:  0.2391° */
    {    2188,   15360 }, /* 112:  0.2250° */
    {    2043,   15424 }, /* 113:  0.2109° */
    {    1899,   15488 }, /* 114:  0.1969° */
    {    1756,   15552 }, /* 115:  0.1828° */
    {    1614,   15616 }, /* 116:  0.1687° */
    {    1473,   15680 }, /* 117:  0.1547° */
    {    1334,   15744 }, /* 118:  0.1406° */
    {    1196,   15808 }, /* 119:  0.1266° */
    {    1058,   15872 }, /* 120:  0.1125° */
    {     922,   15936 }, /* 121:  0.0984° */
    {     787,   16000 }, /* 122:  0.0844° */
    {     653,   16064 }, /* 123:  0.0703° */
    {     521,   16128 }, /* 124:  0.0562° */
    {     389,   16192 }, /* 125:  0.0422° */
    {     258,   16256 }, /* 126:  0.0281° */
    {     128,   16320 }, /* 127:  0.0141° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0141° */
    {    -254,   16512 }, /* 130: -0.0281° */
    {    -380,   16576 }, /* 131: -0.0422° */
    {    -505,   16640 }, /* 132: -0.0563° */
    {    -629,   16704 }, /* 133: -0.0703° */
    {    -751,   16768 }, /* 134: -0.0844° */
    {    -873,   16832 }, /* 135: -0.0984° */
    {    -995,   16896 }, /* 136: -0.1125° */
    {   -1115,   16960 }, /* 137: -0.1266° */
    {   -1234,   17024 }, /* 138: -0.1406° */
    {   -1352,   17088 }, /* 139: -0.1547° */
    {   -1470,   17152 }, /* 140: -0.1688° */
    {   -1587,   17216 }, /* 141: -0.1828° */
    {   -1702,   17280 }, /* 142: -0.1969° */
    {   -1817,   17344 }, /* 143: -0.2109° */
    {   -1931,   17408 }, /* 144: -0.2250° */
    {   -2045,   17472 }, /* 145: -0.2391° */
    {   -2157,   17536 }, /* 146: -0.2531° */
    {   -2269,   17600 }, /* 147: -0.2672° */
    {   -2379,   17664 }, /* 148: -0.2813° */
    {   -2489,   17728 }, /* 149: -0.2953° */
    {   -2599,   17792 }, /* 150: -0.3094° */
    {   -2707,   17856 }, /* 151: -0.3234° */
    {   -2815,   17920 }, /* 152: -0.3375° */
    {   -2922,   17984 }, /* 153: -0.3516° */
    {   -3028,   18048 }, /* 154: -0.3656° */
    {   -3133,   18112 }, /* 155: -0.3797° */
    {   -3238,   18176 }, /* 156: -0.3938° */
    {   -3342,   18240 }, /* 157: -0.4078° */
    {   -3445,   18304 }, /* 158: -0.4219° */
    {   -3548,   18368 }, /* 159: -0.4359° */
    {   -3649,   18432 }, /* 160: -0.4500° */
    {   -3750,   18496 }, /* 161: -0.4641° */
    {   -3851,   18560 }, /* 162: -0.4781° */
    {   -3951,   18624 }, /* 163: -0.4922° */
    {   -4050,   18688 }, /* 164: -0.5063° */
    {   -4148,   18752 }, /* 165: -0.5203° */
    {   -4246,   18816 }, /* 166: -0.5344° */
    {   -4343,   18880 }, /* 167: -0.5484° */
    {   -4439,   18944 }, /* 168: -0.5625° */
    {   -4535,   19008 }, /* 169: -0.5766° */
    {   -4630,   19073 }, /* 170: -0.5906° */
    {   -4724,   19137 }, /* 171: -0.6047° */
    {   -4818,   19201 }, /* 172: -0.6188° */
    {   -4911,   19265 }, /* 173: -0.6328° */
    {   -5004,   19329 }, /* 174: -0.6469° */
    {   -5096,   19393 }, /* 175: -0.6609° */
    {   -5187,   19457 }, /* 176: -0.6750° */
    {   -5278,   19521 }, /* 177: -0.6891° */
    {   -5368,   19585 }, /* 178: -0.7031° */
    {   -5458,   19649 }, /* 179: -0.7172° */
    {   -5547,   19713 }, /* 180: -0.7313° */
    {   -5636,   19777 }, /* 181: -0.7453° */
    {   -5724,   19841 }, /* 182: -0.7594° */
    {   -5811,   19905 }, /* 183: -0.7734° */
    {   -5898,   19969 }, /* 184: -0.7875° */
    {   -5984,   20033 }, /* 185: -0.8016° */
    {   -6070,   20098 }, /* 186: -0.8156° */
    {   -6155,   20162 }, /* 187: -0.8297° */
    {   -6239,   20226 }, /* 188: -0.8438° */
    {   -6323,   20290 }, /* 189: -0.8578° */
    {   -6407,   20354 }, /* 190: -0.8719° */
    {   -6490,   20418 }, /* 191: -0.8859° */
    {   -6573,   20482 }, /* 192: -0.9000° */
    {   -6655,   20546 }, /* 193: -0.9141° */
    {   -6736,   20610 }, /* 194: -0.9281° */
    {   -6817,   20674 }, /* 195: -0.9422° */
    {   -6898,   20738 }, /* 196: -0.9563° */
    {   -6978,   20803 }, /* 197: -0.9703° */
    {   -7057,   20867 }, /* 198: -0.9844° */
    {   -7136,   20931 }, /* 199: -0.9984° */
    {   -7215,   20995 }, /* 200: -1.0125° */
    {   -7293,   21059 }, /* 201: -1.0266° */
    {   -7370,   21123 }, /* 202: -1.0406° */
    {   -7448,   21187 }, /* 203: -1.0547° */
    {   -7524,   21251 }, /* 204: -1.0688° */
    {   -7601,   21315 }, /* 205: -1.0828° */
    {   -7676,   21379 }, /* 206: -1.0969° */
    {   -7752,   21444 }, /* 207: -1.1109° */
    {   -7827,   21508 }, /* 208: -1.1250° */
    {   -7901,   21572 }, /* 209: -1.1391° */
    {   -7975,   21636 }, /* 210: -1.1531° */
    {   -8049,   21700 }, /* 211: -1.1672° */
    {   -8122,   21764 }, /* 212: -1.1813° */
    {   -8194,   21828 }, /* 213: -1.1953° */
    {   -8267,   21892 }, /* 214: -1.2094° */
    {   -8339,   21957 }, /* 215: -1.2234° */
    {   -8410,   22021 }, /* 216: -1.2375° */
    {   -8481,   22085 }, /* 217: -1.2516° */
    {   -8552,   22149 }, /* 218: -1.2656° */
    {   -8622,   22213 }, /* 219: -1.2797° */
    {   -8692,   22277 }, /* 220: -1.2938° */
    {   -8761,   22341 }, /* 221: -1.3078° */
    {   -8830,   22406 }, /* 222: -1.3219° */
    {   -8899,   22470 }, /* 223: -1.3359° */
    {   -8967,   22534 }, /* 224: -1.3500° */
    {   -9035,   22598 }, /* 225: -1.3641° */
    {   -9103,   22662 }, /* 226: -1.3781° */
    {   -9170,   22726 }, /* 227: -1.3922° */
    {   -9237,   22790 }, /* 228: -1.4063° */
    {   -9303,   22855 }, /* 229: -1.4203° */
    {   -9369,   22919 }, /* 230: -1.4344° */
    {   -9435,   22983 }, /* 231: -1.4484° */
    {   -9500,   23047 }, /* 232: -1.4625° */
    {   -9565,   23111 }, /* 233: -1.4766° */
    {   -9629,   23175 }, /* 234: -1.4906° */
    {   -9694,   23240 }, /* 235: -1.5047° */
    {   -9757,   23304 }, /* 236: -1.5188° */
    {   -9821,   23368 }, /* 237: -1.5328° */
    {   -9884,   23432 }, /* 238: -1.5469° */
    {   -9947,   23496 }, /* 239: -1.5609° */
    {  -10009,   23560 }, /* 240: -1.5750° */
    {  -10072,   23625 }, /* 241: -1.5891° */
    {  -10133,   23689 }, /* 242: -1.6031° */
    {  -10195,   23753 }, /* 243: -1.6172° */
    {  -10256,   23817 }, /* 244: -1.6313° */
    {  -10317,   23881 }, /* 245: -1.6453° */
    {  -10377,   23946 }, /* 246: -1.6594° */
    {  -10438,   24010 }, /* 247: -1.6734° */
    {  -10498,   24074 }, /* 248: -1.6875° */
    {  -10557,   24138 }, /* 249: -1.7016° */
    {  -10616,   24202 }, /* 250: -1.7156° */
    {  -10675,   24267 }, /* 251: -1.7297° */
    {  -10734,   24331 }, /* 252: -1.7438° */
    {  -10792,   24395 }, /* 253: -1.7578° */
    {  -10850,   24459 }, /* 254: -1.7719° */
    {  -10908,   24524 }  /* 255: -1.7859 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 64000 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 6400 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8194 }, /*   0:  1.4063° */
    {   32260,    8258 }, /*   1:  1.3953° */
    {   31760,    8322 }, /*   2:  1.3843° */
    {   31268,    8386 }, /*   3:  1.3733° */
    {   30783,    8450 }, /*   4:  1.3623° */
    {   30305,    8514 }, /*   5:  1.3513° */
    {   29835,    8578 }, /*   6:  1.3403° */
    {   29371,    8642 }, /*   7:  1.3293° */
    {   28915,    8706 }, /*   8:  1.3184° */
    {   28465,    8770 }, /*   9:  1.3074° */
    {   28021,    8834 }, /*  10:  1.2964° */
    {   27584,    8898 }, /*  11:  1.2854° */
    {   27153,    8962 }, /*  12:  1.2744° */
    {   26728,    9026 }, /*  13:  1.2634° */
    {   26310,    9090 }, /*  14:  1.2524° */
    {   25897,    9154 }, /*  15:  1.2415° */
    {   25490,    9218 }, /*  16:  1.2305° */
    {   25088,    9282 }, /*  17:  1.2195° */
    {   24692,    9346 }, /*  18:  1.2085° */
    {   24301,    9410 }, /*  19:  1.1975° */
    {   23916,    9474 }, /*  20:  1.1865° */
    {   23536,    9538 }, /*  21:  1.1755° */
    {   23160,    9601 }, /*  22:  1.1646° */
    {   22790,    9665 }, /*  23:  1.1536° */
    {   22425,    9729 }, /*  24:  1.1426° */
    {   22064,    9793 }, /*  25:  1.1316° */
    {   21708,    9857 }, /*  26:  1.1206° */
    {   21357,    9921 }, /*  27:  1.1096° */
    {   21010,    9985 }, /*  28:  1.0986° */
    {   20668,   10049 }, /*  29:  1.0876° */
    {   20330,   10113 }, /*  30:  1.0767° */
    {   19996,   10177 }, /*  31:  1.0657° */
    {   19666,   10241 }, /*  32:  1.0547° */
    {   19341,   10305 }, /*  33:  1.0437° */
    {   19019,   10369 }, /*  34:  1.0327° */
    {   18701,   10433 }, /*  35:  1.0217° */
    {   18388,   10497 }, /*  36:  1.0107° */
    {   18078,   10561 }, /*  37:  0.9998° */
    {   17772,   10625 }, /*  38:  0.9888° */
    {   17469,   10689 }, /*  39:  0.9778° */
    {   17170,   10753 }, /*  40:  0.9668° */
    {   16875,   10817 }, /*  41:  0.9558° */
    {   16583,   10881 }, /*  42:  0.9448° */
    {   16294,   10945 }, /*  43:  0.9338° */
    {   16009,   11009 }, /*  44:  0.9229° */
    {   15727,   11073 }, /*  45:  0.9119° */
    {   15449,   11137 }, /*  46:  0.9009° */
    {   15173,   11201 }, /*  47:  0.8899° */
    {   14901,   11265 }, /*  48:  0.8789° */
    {   14631,   11329 }, /*  49:  0.8679° */
    {   14365,   11393 }, /*  50:  0.8569° */
    {   14102,   11457 }, /*  51:  0.8459° */
    {   13842,   11521 }, /*  52:  0.8350° */
    {   13584,   11585 }, /*  53:  0.8240° */
    {   13329,   11649 }, /*  54:  0.8130° */
    {   13078,   11713 }, /*  55:  0.8020° */
    {   12828,   11777 }, /*  56:  0.7910° */
    {   12582,   11841 }, /*  57:  0.7800° */
    {   12338,   11905 }, /*  58:  0.7690° */
    {   12097,   11969 }, /*  59:  0.7581° */
    {   11858,   12033 }, /*  60:  0.7471° */
    {   11622,   12097 }, /*  61:  0.7361° */
    {   11389,   12160 }, /*  62:  0.7251° */
    {   11158,   12224 }, /*  63:  0.7141° */
    {   10929,   12288 }, /*  64:  0.7031° */
    {   10702,   12352 }, /*  65:  0.6921° */
    {   10478,   12416 }, /*  66:  0.6812° */
    {   10256,   12480 }, /*  67:  0.6702° */
    {   10037,   12544 }, /*  68:  0.6592° */
    {    9820,   12608 }, /*  69:  0.6482° */
    {    9605,   12672 }, /*  70:  0.6372° */
    {    9392,   12736 }, /*  71:  0.6262° */
    {    9181,   12800 }, /*  72:  0.6152° */
    {    8972,   12864 }, /*  73:  0.6042° */
    {    8765,   12928 }, /*  74:  0.5933° */
    {    8561,   12992 }, /*  75:  0.5823° */
    {    8358,   13056 }, /*  76:  0.5713° */
    {    8157,   13120 }, /*  77:  0.5603° */
    {    7959,   13184 }, /*  78:  0.5493° */
    {    7762,   13248 }, /*  79:  0.5383° */
    {    7567,   13312 }, /*  80:  0.5273° */
    {    7374,   13376 }, /*  81:  0.5164° */
    {    7183,   13440 }, /*  82:  0.5054° */
    {    6993,   13504 }, /*  83:  0.4944° */
    {    6806,   13568 }, /*  84:  0.4834° */
    {    6620,   13632 }, /*  85:  0.4724° */
    {    6436,   13696 }, /*  86:  0.4614° */
    {    6253,   13760 }, /*  87:  0.4504° */
    {    6073,   13824 }, /*  88:  0.4395° */
    {    5894,   13888 }, /*  89:  0.4285° */
    {    5716,   13952 }, /*  90:  0.4175° */
    {    5540,   14016 }, /*  91:  0.4065° */
    {    5366,   14080 }, /*  92:  0.3955° */
    {    5194,   14144 }, /*  93:  0.3845° */
    {    5022,   14208 }, /*  94:  0.3735° */
    {    4853,   14272 }, /*  95:  0.3625° */
    {    4685,   14336 }, /*  96:  0.3516° */
    {    4518,   14400 }, /*  97:  0.3406° */
    {    4353,   14464 }, /*  98:  0.3296° */
    {    4190,   14528 }, /*  99:  0.3186° */
    {    4027,   14592 }, /* 100:  0.3076° */
    {    3867,   14656 }, /* 101:  0.2966° */
    {    3707,   14720 }, /* 102:  0.2856° */
    {    3549,   14784 }, /* 103:  0.2747° */
    {    3393,   14848 }, /* 104:  0.2637° */
    {    3237,   14912 }, /* 105:  0.2527° */
    {    3083,   14976 }, /* 106:  0.2417° */
    {    2931,   15040 }, /* 107:  0.2307° */
    {    2779,   15104 }, /* 108:  0.2197° */
    {    2629,   15168 }, /* 109:  0.2087° */
    {    2480,   15232 }, /* 110:  0.1978° */
    {    2333,   15296 }, /* 111:  0.1868° */
    {    2186,   15360 }, /* 112:  0.1758° */
    {    2041,   15424 }, /* 113:  0.1648° */
    {    1897,   15488 }, /* 114:  0.1538° */
    {    1754,   15552 }, /* 115:  0.1428° */
    {    1613,   15616 }, /* 116:  0.1318° */
    {    1472,   15680 }, /* 117:  0.1208° */
    {    1333,   15744 }, /* 118:  0.1099° */
    {    1195,   15808 }, /* 119:  0.0989° */
    {    1058,   15872 }, /* 120:  0.0879° */
    {     922,   15936 }, /* 121:  0.0769° */
    {     787,   16000 }, /* 122:  0.0659° */
    {     653,   16064 }, /* 123:  0.0549° */
    {     520,   16128 }, /* 124:  0.0439° */
    {     389,   16192 }, /* 125:  0.0330° */
    {     258,   16256 }, /* 126:  0.0220° */
    {     128,   16320 }, /* 127:  0.0110° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0110° */
    {    -254,   16512 }, /* 130: -0.0220° */
    {    -380,   16576 }, /* 131: -0.0330° */
    {    -504,   16640 }, /* 132: -0.0439° */
    {    -628,   16704 }, /* 133: -0.0549° */
    {    -751,   16768 }, /* 134: -0.0659° */
    {    -873,   16832 }, /* 135: -0.0769° */
    {    -994,   16896 }, /* 136: -0.0879° */
    {   -1114,   16960 }, /* 137: -0.0989° */
    {   -1233,   17024 }, /* 138: -0.1099° */
    {   -1351,   17088 }, /* 139: -0.1209° */
    {   -1469,   17152 }, /* 140: -0.1318° */
    {   -1585,   17216 }, /* 141: -0.1428° */
    {   -1701,   17280 }, /* 142: -0.1538° */
    {   -1816,   17344 }, /* 143: -0.1648° */
    {   -1930,   17408 }, /* 144: -0.1758° */
    {   -2043,   17472 }, /* 145: -0.1868° */
    {   -2155,   17536 }, /* 146: -0.1978° */
    {   -2267,   17600 }, /* 147: -0.2087° */
    {   -2377,   17664 }, /* 148: -0.2197° */
    {   -2487,   17728 }, /* 149: -0.2307° */
    {   -2596,   17792 }, /* 150: -0.2417° */
    {   -2705,   17856 }, /* 151: -0.2527° */
    {   -2812,   17920 }, /* 152: -0.2637° */
    {   -2919,   17984 }, /* 153: -0.2747° */
    {   -3025,   18048 }, /* 154: -0.2856° */
    {   -3130,   18112 }, /* 155: -0.2966° */
    {   -3235,   18176 }, /* 156: -0.3076° */
    {   -3339,   18240 }, /* 157: -0.3186° */
    {   -3442,   18304 }, /* 158: -0.3296° */
    {   -3544,   18368 }, /* 159: -0.3406° */
    {   -3646,   18432 }, /* 160: -0.3516° */
    {   -3747,   18496 }, /* 161: -0.3625° */
    {   -3847,   18560 }, /* 162: -0.3735° */
    {   -3947,   18624 }, /* 163: -0.3845° */
    {   -4046,   18688 }, /* 164: -0.3955° */
    {   -4144,   18752 }, /* 165: -0.4065° */
    {   -4241,   18816 }, /* 166: -0.4175° */
    {   -4338,   18880 }, /* 167: -0.4285° */
    {   -4435,   18944 }, /* 168: -0.4395° */
    {   -4530,   19008 }, /* 169: -0.4504° */
    {   -4625,   19072 }, /* 170: -0.4614° */
    {   -4720,   19136 }, /* 171: -0.4724° */
    {   -4813,   19200 }, /* 172: -0.4834° */
    {   -4906,   19264 }, /* 173: -0.4944° */
    {   -4999,   19328 }, /* 174: -0.5054° */
    {   -5091,   19392 }, /* 175: -0.5164° */
    {   -5182,   19456 }, /* 176: -0.5273° */
    {   -5273,   19520 }, /* 177: -0.5383° */
    {   -5363,   19584 }, /* 178: -0.5493° */
    {   -5452,   19648 }, /* 179: -0.5603° */
    {   -5541,   19712 }, /* 180: -0.5713° */
    {   -5629,   19777 }, /* 181: -0.5823° */
    {   -5717,   19841 }, /* 182: -0.5933° */
    {   -5804,   19905 }, /* 183: -0.6042° */
    {   -5891,   19969 }, /* 184: -0.6152° */
    {   -5977,   20033 }, /* 185: -0.6262° */
    {   -6063,   20097 }, /* 186: -0.6372° */
    {   -6148,   20161 }, /* 187: -0.6482° */
    {   -6232,   20225 }, /* 188: -0.6592° */
    {   -6316,   20289 }, /* 189: -0.6702° */
    {   -6400,   20353 }, /* 190: -0.6812° */
    {   -6483,   20417 }, /* 191: -0.6921° */
    {   -6565,   20481 }, /* 192: -0.7031° */
    {   -6647,   20545 }, /* 193: -0.7141° */
    {   -6728,   20609 }, /* 194: -0.7251° */
    {   -6809,   20673 }, /* 195: -0.7361° */
    {   -6889,   20737 }, /* 196: -0.7471° */
    {   -6969,   20801 }, /* 197: -0.7581° */
    {   -7049,   20865 }, /* 198: -0.7690° */
    {   -7128,   20929 }, /* 199: -0.7800° */
    {   -7206,   20994 }, /* 200: -0.7910° */
    {   -7284,   21058 }, /* 201: -0.8020° */
    {   -7361,   21122 }, /* 202: -0.8130° */
    {   -7438,   21186 }, /* 203: -0.8240° */
    {   -7515,   21250 }, /* 204: -0.8350° */
    {   -7591,   21314 }, /* 205: -0.8459° */
    {   -7667,   21378 }, /* 206: -0.8569° */
    {   -7742,   21442 }, /* 207: -0.8679° */
    {   -7817,   21506 }, /* 208: -0.8789° */
    {   -7891,   21570 }, /* 209: -0.8899° */
    {   -7965,   21634 }, /* 210: -0.9009° */
    {   -8038,   21698 }, /* 211: -0.9119° */
    {   -8111,   21762 }, /* 212: -0.9229° */
    {   -8184,   21826 }, /* 213: -0.9338° */
    {   -8256,   21890 }, /* 214: -0.9448° */
    {   -8328,   21955 }, /* 215: -0.9558° */
    {   -8399,   22019 }, /* 216: -0.9668° */
    {   -8470,   22083 }, /* 217: -0.9778° */
    {   -8541,   22147 }, /* 218: -0.9888° */
    {   -8611,   22211 }, /* 219: -0.9998° */
    {   -8680,   22275 }, /* 220: -1.0107° */
    {   -8750,   22339 }, /* 221: -1.0217° */
    {   -8818,   22403 }, /* 222: -1.0327° */
    {   -8887,   22467 }, /* 223: -1.0437° */
    {   -8955,   22531 }, /* 224: -1.0547° */
    {   -9023,   22595 }, /* 225: -1.0657° */
    {   -9090,   22660 }, /* 226: -1.0767° */
    {   -9157,   22724 }, /* 227: -1.0876° */
    {   -9224,   22788 }, /* 228: -1.0986° */
    {   -9290,   22852 }, /* 229: -1.1096° */
    {   -9356,   22916 }, /* 230: -1.1206° */
    {   -9421,   22980 }, /* 231: -1.1316° */
    {   -9487,   23044 }, /* 232: -1.1426° */
    {   -9551,   23108 }, /* 233: -1.1536° */
    {   -9616,   23172 }, /* 234: -1.1646° */
    {   -9680,   23236 }, /* 235: -1.1755° */
    {   -9744,   23301 }, /* 236: -1.1865° */
    {   -9807,   23365 }, /* 237: -1.1975° */
    {   -9870,   23429 }, /* 238: -1.2085° */
    {   -9933,   23493 }, /* 239: -1.2195° */
    {   -9995,   23557 }, /* 240: -1.2305° */
    {  -10057,   23621 }, /* 241: -1.2415° */
    {  -10119,   23685 }, /* 242: -1.2524° */
    {  -10180,   23749 }, /* 243: -1.2634° */
    {  -10241,   23813 }, /* 244: -1.2744° */
    {  -10302,   23878 }, /* 245: -1.2854° */
    {  -10362,   23942 }, /* 246: -1.2964° */
    {  -10422,   24006 }, /* 247: -1.3074° */
    {  -10482,   24070 }, /* 248: -1.3184° */
    {  -10541,   24134 }, /* 249: -1.3293° */
    {  -10600,   24198 }, /* 250: -1.3403° */
    {  -10659,   24262 }, /* 251: -1.3513° */
    {  -10718,   24326 }, /* 252: -1.3623° */
    {  -10776,   24391 }, /* 253: -1.3733° */
    {  -10834,   24455 }, /* 254: -1.3843° */
    {  -10891,   24519 }  /* 255: -1.3953 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 64000 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 6400 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8195 }, /*   0:  1.6875° */
    {   32260,    8259 }, /*   1:  1.6743° */
    {   31760,    8323 }, /*   2:  1.6611° */
    {   31268,    8387 }, /*   3:  1.6479° */
    {   30783,    8451 }, /*   4:  1.6348° */
    {   30306,    8515 }, /*   5:  1.6216° */
    {   29835,    8579 }, /*   6:  1.6084° */
    {   29372,    8643 }, /*   7:  1.5952° */
    {   28916,    8707 }, /*   8:  1.5820° */
    {   28466,    8771 }, /*   9:  1.5688° */
    {   28022,    8835 }, /*  10:  1.5557° */
    {   27585,    8899 }, /*  11:  1.5425° */
    {   27155,    8963 }, /*  12:  1.5293° */
    {   26730,    9027 }, /*  13:  1.5161° */
    {   26311,    9091 }, /*  14:  1.5029° */
    {   25898,    9155 }, /*  15:  1.4897° */
    {   25491,    9219 }, /*  16:  1.4766° */
    {   25090,    9283 }, /*  17:  1.4634° */
    {   24694,    9346 }, /*  18:  1.4502° */
    {   24303,    9410 }, /*  19:  1.4370° */
    {   23918,    9474 }, /*  20:  1.4238° */
    {   23538,    9538 }, /*  21:  1.4106° */
    {   23162,    9602 }, /*  22:  1.3975° */
    {   22792,    9666 }, /*  23:  1.3843° */
    {   22427,    9730 }, /*  24:  1.3711° */
    {   22067,    9794 }, /*  25:  1.3579° */
    {   21711,    9858 }, /*  26:  1.3447° */
    {   21359,    9922 }, /*  27:  1.3315° */
    {   21013,    9986 }, /*  28:  1.3184° */
    {   20670,   10050 }, /*  29:  1.3052° */
    {   20332,   10114 }, /*  30:  1.2920° */
    {   19998,   10178 }, /*  31:  1.2788° */
    {   19669,   10242 }, /*  32:  1.2656° */
    {   19343,   10306 }, /*  33:  1.2524° */
    {   19022,   10370 }, /*  34:  1.2393° */
    {   18704,   10434 }, /*  35:  1.2261° */
    {   18391,   10498 }, /*  36:  1.2129° */
    {   18081,   10562 }, /*  37:  1.1997° */
    {   17774,   10626 }, /*  38:  1.1865° */
    {   17472,   10690 }, /*  39:  1.1733° */
    {   17173,   10754 }, /*  40:  1.1602° */
    {   16878,   10818 }, /*  41:  1.1470° */
    {   16586,   10882 }, /*  42:  1.1338° */
    {   16297,   10946 }, /*  43:  1.1206° */
    {   16012,   11010 }, /*  44:  1.1074° */
    {   15730,   11074 }, /*  45:  1.0942° */
    {   15452,   11137 }, /*  46:  1.0811° */
    {   15176,   11201 }, /*  47:  1.0679° */
    {   14904,   11265 }, /*  48:  1.0547° */
    {   14635,   11329 }, /*  49:  1.0415° */
    {   14368,   11393 }, /*  50:  1.0283° */
    {   14105,   11457 }, /*  51:  1.0151° */
    {   13845,   11521 }, /*  52:  1.0020° */
    {   13587,   11585 }, /*  53:  0.9888° */
    {   13333,   11649 }, /*  54:  0.9756° */
    {   13081,   11713 }, /*  55:  0.9624° */
    {   12832,   11777 }, /*  56:  0.9492° */
    {   12585,   11841 }, /*  57:  0.9360° */
    {   12341,   11905 }, /*  58:  0.9229° */
    {   12100,   11969 }, /*  59:  0.9097° */
    {   11861,   12033 }, /*  60:  0.8965° */
    {   11625,   12097 }, /*  61:  0.8833° */
    {   11392,   12161 }, /*  62:  0.8701° */
    {   11160,   12225 }, /*  63:  0.8569° */
    {   10932,   12289 }, /*  64:  0.8438° */
    {   10705,   12353 }, /*  65:  0.8306° */
    {   10481,   12417 }, /*  66:  0.8174° */
    {   10259,   12481 }, /*  67:  0.8042° */
    {   10040,   12545 }, /*  68:  0.7910° */
    {    9822,   12609 }, /*  69:  0.7778° */
    {    9607,   12673 }, /*  70:  0.7647° */
    {    9394,   12737 }, /*  71:  0.7515° */
    {    9184,   12801 }, /*  72:  0.7383° */
    {    8975,   12865 }, /*  73:  0.7251° */
    {    8768,   12928 }, /*  74:  0.7119° */
    {    8563,   12992 }, /*  75:  0.6987° */
    {    8361,   13056 }, /*  76:  0.6855° */
    {    8160,   13120 }, /*  77:  0.6724° */
    {    7961,   13184 }, /*  78:  0.6592° */
    {    7765,   13248 }, /*  79:  0.6460° */
    {    7570,   13312 }, /*  80:  0.6328° */
    {    7377,   13376 }, /*  81:  0.6196° */
    {    7185,   13440 }, /*  82:  0.6064° */
    {    6996,   13504 }, /*  83:  0.5933° */
    {    6808,   13568 }, /*  84:  0.5801° */
    {    6622,   13632 }, /*  85:  0.5669° */
    {    6438,   13696 }, /*  86:  0.5537° */
    {    6256,   13760 }, /*  87:  0.5405° */
    {    6075,   13824 }, /*  88:  0.5273° */
    {    5896,   13888 }, /*  89:  0.5142° */
    {    5718,   13952 }, /*  90:  0.5010° */
    {    5543,   14016 }, /*  91:  0.4878° */
    {    5368,   14080 }, /*  92:  0.4746° */
    {    5196,   14144 }, /*  93:  0.4614° */
    {    5024,   14208 }, /*  94:  0.4482° */
    {    4855,   14272 }, /*  95:  0.4351° */
    {    4687,   14336 }, /*  96:  0.4219° */
    {    4520,   14400 }, /*  97:  0.4087° */
    {    4355,   14464 }, /*  98:  0.3955° */
    {    4191,   14528 }, /*  99:  0.3823° */
    {    4029,   14592 }, /* 100:  0.3691° */
    {    3868,   14656 }, /* 101:  0.3560° */
    {    3709,   14720 }, /* 102:  0.3428° */
    {    3551,   14784 }, /* 103:  0.3296° */
    {    3394,   14848 }, /* 104:  0.3164° */
    {    3239,   14912 }, /* 105:  0.3032° */
    {    3085,   14976 }, /* 106:  0.2900° */
    {    2932,   15040 }, /* 107:  0.2769° */
    {    2781,   15104 }, /* 108:  0.2637° */
    {    2630,   15168 }, /* 109:  0.2505° */
    {    2481,   15232 }, /* 110:  0.2373° */
    {    2334,   15296 }, /* 111:  0.2241° */
    {    2187,   15360 }, /* 112:  0.2109° */
    {    2042,   15424 }, /* 113:  0.1978° */
    {    1898,   15488 }, /* 114:  0.1846° */
    {    1755,   15552 }, /* 115:  0.1714° */
    {    1614,   15616 }, /* 116:  0.1582° */
    {    1473,   15680 }, /* 117:  0.1450° */
    {    1334,   15744 }, /* 118:  0.1318° */
    {    1195,   15808 }, /* 119:  0.1187° */
    {    1058,   15872 }, /* 120:  0.1055° */
    {     922,   15936 }, /* 121:  0.0923° */
    {     787,   16000 }, /* 122:  0.0791° */
    {     653,   16063 }, /* 123:  0.0659° */
    {     521,   16127 }, /* 124:  0.0527° */
    {     389,   16191 }, /* 125:  0.0396° */
    {     258,   16255 }, /* 126:  0.0264° */
    {     128,   16319 }, /* 127:  0.0132° */
    {       0,   16383 }, /* 128:  0.0000° */
    {    -127,   16447 }, /* 129: -0.0132° */
    {    -254,   16511 }, /* 130: -0.0264° */
    {    -380,   16575 }, /* 131: -0.0395° */
    {    -505,   16639 }, /* 132: -0.0527° */
    {    -628,   16703 }, /* 133: -0.0659° */
    {    -751,   16768 }, /* 134: -0.0791° */
    {    -873,   16832 }, /* 135: -0.0923° */
    {    -994,   16896 }, /* 136: -0.1055° */
    {   -1114,   16960 }, /* 137: -0.1186° */
    {   -1234,   17024 }, /* 138: -0.1318° */
    {   -1352,   17088 }, /* 139: -0.1450° */
    {   -1469,   17152 }, /* 140: -0.1582° */
    {   -1586,   17216 }, /* 141: -0.1714° */
    {   -1702,   17280 }, /* 142: -0.1846° */
    {   -1817,   17344 }, /* 143: -0.1978° */
    {   -1931,   17408 }, /* 144: -0.2109° */
    {   -2044,   17472 }, /* 145: -0.2241° */
    {   -2156,   17536 }, /* 146: -0.2373° */
    {   -2268,   17600 }, /* 147: -0.2505° */
    {   -2379,   17664 }, /* 148: -0.2637° */
    {   -2489,   17728 }, /* 149: -0.2769° */
    {   -2598,   17792 }, /* 150: -0.2900° */
    {   -2706,   17856 }, /* 151: -0.3032° */
    {   -2814,   17920 }, /* 152: -0.3164° */
    {   -2921,   17984 }, /* 153: -0.3296° */
    {   -3027,   18048 }, /* 154: -0.3428° */
    {   -3132,   18112 }, /* 155: -0.3560° */
    {   -3237,   18176 }, /* 156: -0.3691° */
    {   -3341,   18240 }, /* 157: -0.3823° */
    {   -3444,   18304 }, /* 158: -0.3955° */
    {   -3547,   18368 }, /* 159: -0.4087° */
    {   -3648,   18432 }, /* 160: -0.4219° */
    {   -3749,   18496 }, /* 161: -0.4351° */
    {   -3850,   18560 }, /* 162: -0.4482° */
    {   -3949,   18624 }, /* 163: -0.4614° */
    {   -4048,   18688 }, /* 164: -0.4746° */
    {   -4147,   18752 }, /* 165: -0.4878° */
    {   -4244,   18816 }, /* 166: -0.5010° */
    {   -4341,   18880 }, /* 167: -0.5142° */
    {   -4438,   18944 }, /* 168: -0.5273° */
    {   -4533,   19008 }, /* 169: -0.5405° */
    {   -4628,   19072 }, /* 170: -0.5537° */
    {   -4723,   19136 }, /* 171: -0.5669° */
    {   -4817,   19200 }, /* 172: -0.5801° */
    {   -4910,   19265 }, /* 173: -0.5933° */
    {   -5002,   19329 }, /* 174: -0.6064° */
    {   -5094,   19393 }, /* 175: -0.6196° */
    {   -5186,   19457 }, /* 176: -0.6328° */
    {   -5277,   19521 }, /* 177: -0.6460° */
    {   -5367,   19585 }, /* 178: -0.6592° */
    {   -5456,   19649 }, /* 179: -0.6724° */
    {   -5545,   19713 }, /* 180: -0.6855° */
    {   -5634,   19777 }, /* 181: -0.6987° */
    {   -5722,   19841 }, /* 182: -0.7119° */
    {   -5809,   19905 }, /* 183: -0.7251° */
    {   -5896,   19969 }, /* 184: -0.7383° */
    {   -5982,   20033 }, /* 185: -0.7515° */
    {   -6067,   20097 }, /* 186: -0.7646° */
    {   -6153,   20161 }, /* 187: -0.7778° */
    {   -6237,   20225 }, /* 188: -0.7910° */
    {   -6321,   20289 }, /* 189: -0.8042° */
    {   -6405,   20354 }, /* 190: -0.8174° */
    {   -6488,   20418 }, /* 191: -0.8306° */
    {   -6570,   20482 }, /* 192: -0.8437° */
    {   -6652,   20546 }, /* 193: -0.8569° */
    {   -6734,   20610 }, /* 194: -0.8701° */
    {   -6815,   20674 }, /* 195: -0.8833° */
    {   -6895,   20738 }, /* 196: -0.8965° */
    {   -6975,   20802 }, /* 197: -0.9097° */
    {   -7055,   20866 }, /* 198: -0.9228° */
    {   -7134,   20930 }, /* 199: -0.9360° */
    {   -7212,   20994 }, /* 200: -0.9492° */
    {   -7290,   21058 }, /* 201: -0.9624° */
    {   -7368,   21123 }, /* 202: -0.9756° */
    {   -7445,   21187 }, /* 203: -0.9888° */
    {   -7521,   21251 }, /* 204: -1.0020° */
    {   -7598,   21315 }, /* 205: -1.0151° */
    {   -7673,   21379 }, /* 206: -1.0283° */
    {   -7749,   21443 }, /* 207: -1.0415° */
    {   -7823,   21507 }, /* 208: -1.0547° */
    {   -7898,   21571 }, /* 209: -1.0679° */
    {   -7972,   21635 }, /* 210: -1.0811° */
    {   -8045,   21699 }, /* 211: -1.0942° */
    {   -8118,   21764 }, /* 212: -1.1074° */
    {   -8191,   21828 }, /* 213: -1.1206° */
    {   -8263,   21892 }, /* 214: -1.1338° */
    {   -8335,   21956 }, /* 215: -1.1470° */
    {   -8407,   22020 }, /* 216: -1.1602° */
    {   -8478,   22084 }, /* 217: -1.1733° */
    {   -8548,   22148 }, /* 218: -1.1865° */
    {   -8618,   22212 }, /* 219: -1.1997° */
    {   -8688,   22277 }, /* 220: -1.2129° */
    {   -8758,   22341 }, /* 221: -1.2261° */
    {   -8827,   22405 }, /* 222: -1.2393° */
    {   -8895,   22469 }, /* 223: -1.2524° */
    {   -8963,   22533 }, /* 224: -1.2656° */
    {   -9031,   22597 }, /* 225: -1.2788° */
    {   -9099,   22661 }, /* 226: -1.2920° */
    {   -9166,   22725 }, /* 227: -1.3052° */
    {   -9233,   22790 }, /* 228: -1.3184° */
    {   -9299,   22854 }, /* 229: -1.3315° */
    {   -9365,   22918 }, /* 230: -1.3447° */
    {   -9430,   22982 }, /* 231: -1.3579° */
    {   -9496,   23046 }, /* 232: -1.3711° */
    {   -9561,   23110 }, /* 233: -1.3843° */
    {   -9625,   23174 }, /* 234: -1.3975° */
    {   -9689,   23239 }, /* 235: -1.4106° */
    {   -9753,   23303 }, /* 236: -1.4238° */
    {   -9817,   23367 }, /* 237: -1.4370° */
    {   -9880,   23431 }, /* 238: -1.4502° */
    {   -9942,   23495 }, /* 239: -1.4634° */
    {  -10005,   23559 }, /* 240: -1.4766° */
    {  -10067,   23624 }, /* 241: -1.4897° */
    {  -10129,   23688 }, /* 242: -1.5029° */
    {  -10190,   23752 }, /* 243: -1.5161° */
    {  -10251,   23816 }, /* 244: -1.5293° */
    {  -10312,   23880 }, /* 245: -1.5425° */
    {  -10373,   23944 }, /* 246: -1.5557° */
    {  -10433,   24009 }, /* 247: -1.5688° */
    {  -10493,   24073 }, /* 248: -1.5820° */
    {  -10552,   24137 }, /* 249: -1.5952° */
    {  -10611,   24201 }, /* 250: -1.6084° */
    {  -10670,   24265 }, /* 251: -1.6216° */
    {  -10729,   24329 }, /* 252: -1.6348° */
    {  -10787,   24394 }, /* 253: -1.6479° */
    {  -10845,   24458 }, /* 254: -1.6611° */
    {  -10903,   24522 }  /* 255: -1.6743 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 78125 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 7812 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8193 }, /*   0:  1.1520° */
    {   32260,    8257 }, /*   1:  1.1430° */
    {   31760,    8321 }, /*   2:  1.1340° */
    {   31267,    8385 }, /*   3:  1.1250° */
    {   30782,    8449 }, /*   4:  1.1160° */
    {   30305,    8513 }, /*   5:  1.1070° */
    {   29834,    8577 }, /*   6:  1.0980° */
    {   29371,    8641 }, /*   7:  1.0890° */
    {   28914,    8705 }, /*   8:  1.0800° */
    {   28464,    8769 }, /*   9:  1.0710° */
    {   28020,    8833 }, /*  10:  1.0620° */
    {   27583,    8897 }, /*  11:  1.0530° */
    {   27152,    8961 }, /*  12:  1.0440° */
    {   26727,    9025 }, /*  13:  1.0350° */
    {   26309,    9089 }, /*  14:  1.0260° */
    {   25896,    9153 }, /*  15:  1.0170° */
    {   25488,    9217 }, /*  16:  1.0080° */
    {   25087,    9281 }, /*  17:  0.9990° */
    {   24691,    9345 }, /*  18:  0.9900° */
    {   24300,    9409 }, /*  19:  0.9810° */
    {   23914,    9473 }, /*  20:  0.9720° */
    {   23534,    9537 }, /*  21:  0.9630° */
    {   23159,    9601 }, /*  22:  0.9540° */
    {   22788,    9665 }, /*  23:  0.9450° */
    {   22423,    9729 }, /*  24:  0.9360° */
    {   22062,    9793 }, /*  25:  0.9270° */
    {   21707,    9857 }, /*  26:  0.9180° */
    {   21355,    9921 }, /*  27:  0.9090° */
    {   21008,    9985 }, /*  28:  0.9000° */
    {   20666,   10049 }, /*  29:  0.8910° */
    {   20328,   10113 }, /*  30:  0.8820° */
    {   19994,   10177 }, /*  31:  0.8730° */
    {   19664,   10241 }, /*  32:  0.8640° */
    {   19339,   10305 }, /*  33:  0.8550° */
    {   19017,   10369 }, /*  34:  0.8460° */
    {   18699,   10433 }, /*  35:  0.8370° */
    {   18386,   10497 }, /*  36:  0.8280° */
    {   18076,   10561 }, /*  37:  0.8190° */
    {   17770,   10625 }, /*  38:  0.8100° */
    {   17467,   10689 }, /*  39:  0.8010° */
    {   17168,   10753 }, /*  40:  0.7920° */
    {   16873,   10817 }, /*  41:  0.7830° */
    {   16581,   10880 }, /*  42:  0.7740° */
    {   16292,   10944 }, /*  43:  0.7650° */
    {   16007,   11008 }, /*  44:  0.7560° */
    {   15725,   11072 }, /*  45:  0.7470° */
    {   15446,   11136 }, /*  46:  0.7380° */
    {   15171,   11200 }, /*  47:  0.7290° */
    {   14899,   11264 }, /*  48:  0.7200° */
    {   14629,   11328 }, /*  49:  0.7110° */
    {   14363,   11392 }, /*  50:  0.7020° */
    {   14100,   11456 }, /*  51:  0.6930° */
    {   13839,   11520 }, /*  52:  0.6840° */
    {   13582,   11584 }, /*  53:  0.6750° */
    {   13327,   11648 }, /*  54:  0.6660° */
    {   13075,   11712 }, /*  55:  0.6570° */
    {   12826,   11776 }, /*  56:  0.6480° */
    {   12580,   11840 }, /*  57:  0.6390° */
    {   12336,   11904 }, /*  58:  0.6300° */
    {   12095,   11968 }, /*  59:  0.6210° */
    {   11856,   12032 }, /*  60:  0.6120° */
    {   11620,   12096 }, /*  61:  0.6030° */
    {   11387,   12160 }, /*  62:  0.5940° */
    {   11155,   12224 }, /*  63:  0.5850° */
    {   10927,   12288 }, /*  64:  0.5760° */
    {   10700,   12352 }, /*  65:  0.5670° */
    {   10476,   12416 }, /*  66:  0.5580° */
    {   10254,   12480 }, /*  67:  0.5490° */
    {   10035,   12544 }, /*  68:  0.5400° */
    {    9818,   12608 }, /*  69:  0.5310° */
    {    9602,   12672 }, /*  70:  0.5220° */
    {    9389,   12736 }, /*  71:  0.5130° */
    {    9179,   12800 }, /*  72:  0.5040° */
    {    8970,   12864 }, /*  73:  0.4950° */
    {    8763,   12928 }, /*  74:  0.4860° */
    {    8559,   12992 }, /*  75:  0.4770° */
    {    8356,   13056 }, /*  76:  0.4680° */
    {    8155,   13120 }, /*  77:  0.4590° */
    {    7957,   13184 }, /*  78:  0.4500° */
    {    7760,   13248 }, /*  79:  0.4410° */
    {    7565,   13312 }, /*  80:  0.4320° */
    {    7372,   13376 }, /*  81:  0.4230° */
    {    7181,   13440 }, /*  82:  0.4140° */
    {    6992,   13504 }, /*  83:  0.4050° */
    {    6804,   13568 }, /*  84:  0.3960° */
    {    6618,   13632 }, /*  85:  0.3870° */
    {    6434,   13696 }, /*  86:  0.3780° */
    {    6252,   13760 }, /*  87:  0.3690° */
    {    6071,   13824 }, /*  88:  0.3600° */
    {    5892,   13888 }, /*  89:  0.3510° */
    {    5715,   13952 }, /*  90:  0.3420° */
    {    5539,   14016 }, /*  91:  0.3330° */
    {    5365,   14080 }, /*  92:  0.3240° */
    {    5192,   14144 }, /*  93:  0.3150° */
    {    5021,   14208 }, /*  94:  0.3060° */
    {    4851,   14272 }, /*  95:  0.2970° */
    {    4683,   14336 }, /*  96:  0.2880° */
    {    4517,   14400 }, /*  97:  0.2790° */
    {    4352,   14464 }, /*  98:  0.2700° */
    {    4188,   14528 }, /*  99:  0.2610° */
    {    4026,   14592 }, /* 100:  0.2520° */
    {    3865,   14656 }, /* 101:  0.2430° */
    {    3706,   14720 }, /* 102:  0.2340° */
    {    3548,   14784 }, /* 103:  0.2250° */
    {    3391,   14848 }, /* 104:  0.2160° */
    {    3236,   14912 }, /* 105:  0.2070° */
    {    3082,   14976 }, /* 106:  0.1980° */
    {    2930,   15040 }, /* 107:  0.1890° */
    {    2778,   15104 }, /* 108:  0.1800° */
    {    2628,   15168 }, /* 109:  0.1710° */
    {    2479,   15232 }, /* 110:  0.1620° */
    {    2332,   15296 }, /* 111:  0.1530° */
    {    2186,   15360 }, /* 112:  0.1440° */
    {    2040,   15424 }, /* 113:  0.1350° */
    {    1897,   15488 }, /* 114:  0.1260° */
    {    1754,   15552 }, /* 115:  0.1170° */
    {    1612,   15616 }, /* 116:  0.1080° */
    {    1472,   15680 }, /* 117:  0.0990° */
    {    1333,   15744 }, /* 118:  0.0900° */
    {    1194,   15808 }, /* 119:  0.0810° */
    {    1057,   15872 }, /* 120:  0.0720° */
    {     921,   15936 }, /* 121:  0.0630° */
    {     787,   16000 }, /* 122:  0.0540° */
    {     653,   16064 }, /* 123:  0.0450° */
    {     520,   16128 }, /* 124:  0.0360° */
    {     388,   16192 }, /* 125:  0.0270° */
    {     258,   16256 }, /* 126:  0.0180° */
    {     128,   16320 }, /* 127:  0.0090° */
    {       0,   16384 }, /* 128:  0.0000° */
    {    -127,   16448 }, /* 129: -0.0090° */
    {    -254,   16512 }, /* 130: -0.0180° */
    {    -379,   16576 }, /* 131: -0.0270° */
    {    -504,   16640 }, /* 132: -0.0360° */
    {    -628,   16704 }, /* 133: -0.0450° */
    {    -751,   16768 }, /* 134: -0.0540° */
    {    -872,   16832 }, /* 135: -0.0630° */
    {    -993,   16896 }, /* 136: -0.0720° */
    {   -1113,   16960 }, /* 137: -0.0810° */
    {   -1232,   17024 }, /* 138: -0.0900° */
    {   -1351,   17088 }, /* 139: -0.0990° */
    {   -1468,   17152 }, /* 140: -0.1080° */
    {   -1585,   17216 }, /* 141: -0.1170° */
    {   -1700,   17280 }, /* 142: -0.1260° */
    {   -1815,   17344 }, /* 143: -0.1350° */
    {   -1929,   17408 }, /* 144: -0.1440° */
    {   -2042,   17472 }, /* 145: -0.1530° */
    {   -2154,   17536 }, /* 146: -0.1620° */
    {   -2266,   17600 }, /* 147: -0.1710° */
    {   -2376,   17664 }, /* 148: -0.1800° */
    {   -2486,   17728 }, /* 149: -0.1890° */
    {   -2595,   17792 }, /* 150: -0.1980° */
    {   -2703,   17856 }, /* 151: -0.2070° */
    {   -2811,   17920 }, /* 152: -0.2160° */
    {   -2918,   17984 }, /* 153: -0.2250° */
    {   -3024,   18048 }, /* 154: -0.2340° */
    {   -3129,   18112 }, /* 155: -0.2430° */
    {   -3233,   18176 }, /* 156: -0.2520° */
    {   -3337,   18240 }, /* 157: -0.2610° */
    {   -3440,   18304 }, /* 158: -0.2700° */
    {   -3542,   18368 }, /* 159: -0.2790° */
    {   -3644,   18432 }, /* 160: -0.2880° */
    {   -3745,   18496 }, /* 161: -0.2970° */
    {   -3845,   18560 }, /* 162: -0.3060° */
    {   -3945,   18624 }, /* 163: -0.3150° */
    {   -4044,   18688 }, /* 164: -0.3240° */
    {   -4142,   18752 }, /* 165: -0.3330° */
    {   -4239,   18816 }, /* 166: -0.3420° */
    {   -4336,   18880 }, /* 167: -0.3510° */
    {   -4432,   18944 }, /* 168: -0.3600° */
    {   -4528,   19008 }, /* 169: -0.3690° */
    {   -4623,   19072 }, /* 170: -0.3780° */
    {   -4717,   19136 }, /* 171: -0.3870° */
    {   -4811,   19200 }, /* 172: -0.3960° */
    {   -4904,   19264 }, /* 173: -0.4050° */
    {   -4996,   19328 }, /* 174: -0.4140° */
    {   -5088,   19392 }, /* 175: -0.4230° */
    {   -5179,   19456 }, /* 176: -0.4320° */
    {   -5270,   19520 }, /* 177: -0.4410° */
    {   -5360,   19584 }, /* 178: -0.4500° */
    {   -5449,   19648 }, /* 179: -0.4590° */
    {   -5538,   19712 }, /* 180: -0.4680° */
    {   -5626,   19776 }, /* 181: -0.4770° */
    {   -5714,   19840 }, /* 182: -0.4860° */
    {   -5801,   19904 }, /* 183: -0.4950° */
    {   -5888,   19968 }, /* 184: -0.5040° */
    {   -5974,   20032 }, /* 185: -0.5130° */
    {   -6059,   20096 }, /* 186: -0.5220° */
    {   -6144,   20160 }, /* 187: -0.5310° */
    {   -6229,   20224 }, /* 188: -0.5400° */
    {   -6313,   20288 }, /* 189: -0.5490° */
    {   -6396,   20352 }, /* 190: -0.5580° */
    {   -6479,   20417 }, /* 191: -0.5670° */
    {   -6561,   20481 }, /* 192: -0.5760° */
    {   -6643,   20545 }, /* 193: -0.5850° */
    {   -6724,   20609 }, /* 194: -0.5940° */
    {   -6805,   20673 }, /* 195: -0.6030° */
    {   -6885,   20737 }, /* 196: -0.6120° */
    {   -6965,   20801 }, /* 197: -0.6210° */
    {   -7044,   20865 }, /* 198: -0.6300° */
    {   -7123,   20929 }, /* 199: -0.6390° */
    {   -7202,   20993 }, /* 200: -0.6480° */
    {   -7279,   21057 }, /* 201: -0.6570° */
    {   -7357,   21121 }, /* 202: -0.6660° */
    {   -7434,   21185 }, /* 203: -0.6750° */
    {   -7510,   21249 }, /* 204: -0.6840° */
    {   -7586,   21313 }, /* 205: -0.6930° */
    {   -7662,   21377 }, /* 206: -0.7020° */
    {   -7737,   21441 }, /* 207: -0.7110° */
    {   -7812,   21505 }, /* 208: -0.7200° */
    {   -7886,   21569 }, /* 209: -0.7290° */
    {   -7960,   21633 }, /* 210: -0.7380° */
    {   -8033,   21697 }, /* 211: -0.7470° */
    {   -8106,   21761 }, /* 212: -0.7560° */
    {   -8179,   21825 }, /* 213: -0.7650° */
    {   -8251,   21890 }, /* 214: -0.7740° */
    {   -8322,   21954 }, /* 215: -0.7830° */
    {   -8393,   22018 }, /* 216: -0.7920° */
    {   -8464,   22082 }, /* 217: -0.8010° */
    {   -8535,   22146 }, /* 218: -0.8100° */
    {   -8605,   22210 }, /* 219: -0.8190° */
    {   -8674,   22274 }, /* 220: -0.8280° */
    {   -8744,   22338 }, /* 221: -0.8370° */
    {   -8812,   22402 }, /* 222: -0.8460° */
    {   -8881,   22466 }, /* 223: -0.8550° */
    {   -8949,   22530 }, /* 224: -0.8640° */
    {   -9017,   22594 }, /* 225: -0.8730° */
    {   -9084,   22658 }, /* 226: -0.8820° */
    {   -9151,   22722 }, /* 227: -0.8910° */
    {   -9217,   22786 }, /* 228: -0.9000° */
    {   -9283,   22850 }, /* 229: -0.9090° */
    {   -9349,   22914 }, /* 230: -0.9180° */
    {   -9415,   22979 }, /* 231: -0.9270° */
    {   -9480,   23043 }, /* 232: -0.9360° */
    {   -9544,   23107 }, /* 233: -0.9450° */
    {   -9609,   23171 }, /* 234: -0.9540° */
    {   -9673,   23235 }, /* 235: -0.9630° */
    {   -9736,   23299 }, /* 236: -0.9720° */
    {   -9800,   23363 }, /* 237: -0.9810° */
    {   -9863,   23427 }, /* 238: -0.9900° */
    {   -9925,   23491 }, /* 239: -0.9990° */
    {   -9988,   23555 }, /* 240: -1.0080° */
    {  -10049,   23619 }, /* 241: -1.0170° */
    {  -10111,   23683 }, /* 242: -1.0260° */
    {  -10172,   23747 }, /* 243: -1.0350° */
    {  -10233,   23811 }, /* 244: -1.0440° */
    {  -10294,   23876 }, /* 245: -1.0530° */
    {  -10354,   23940 }, /* 246: -1.0620° */
    {  -10414,   24004 }, /* 247: -1.0710° */
    {  -10474,   24068 }, /* 248: -1.0800° */
    {  -10533,   24132 }, /* 249: -1.0890° */
    {  -10592,   24196 }, /* 250: -1.0980° */
    {  -10651,   24260 }, /* 251: -1.1070° */
    {  -10709,   24324 }, /* 252: -1.1160° */
    {  -10767,   24388 }, /* 253: -1.1250° */
    {  -10825,   24452 }, /* 254: -1.1340° */
    {  -10883,   24516 }  /* 255: -1.1430 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 78125 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 7812 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8194 }, /*   0:  1.3824° */
    {   32260,    8258 }, /*   1:  1.3716° */
    {   31760,    8322 }, /*   2:  1.3608° */
    {   31268,    8386 }, /*   3:  1.3500° */
    {   30783,    8450 }, /*   4:  1.3392° */
    {   30305,    8514 }, /*   5:  1.3284° */
    {   29835,    8578 }, /*   6:  1.3176° */
    {   29371,    8642 }, /*   7:  1.3068° */
    {   28915,    8706 }, /*   8:  1.2960° */
    {   28465,    8770 }, /*   9:  1.2852° */
    {   28021,    8834 }, /*  10:  1.2744° */
    {   27584,    8898 }, /*  11:  1.2636° */
    {   27153,    8962 }, /*  12:  1.2528° */
    {   26728,    9026 }, /*  13:  1.2420° */
    {   26310,    9090 }, /*  14:  1.2312° */
    {   25897,    9154 }, /*  15:  1.2204° */
    {   25489,    9218 }, /*  16:  1.2096° */
    {   25088,    9282 }, /*  17:  1.1988° */
    {   24692,    9346 }, /*  18:  1.1880° */
    {   24301,    9409 }, /*  19:  1.1772° */
    {   23916,    9473 }, /*  20:  1.1664° */
    {   23535,    9537 }, /*  21:  1.1556° */
    {   23160,    9601 }, /*  22:  1.1448° */
    {   22790,    9665 }, /*  23:  1.1340° */
    {   22425,    9729 }, /*  24:  1.1232° */
    {   22064,    9793 }, /*  25:  1.1124° */
    {   21708,    9857 }, /*  26:  1.1016° */
    {   21357,    9921 }, /*  27:  1.0908° */
    {   21010,    9985 }, /*  28:  1.0800° */
    {   20668,   10049 }, /*  29:  1.0692° */
    {   20330,   10113 }, /*  30:  1.0584° */
    {   19996,   10177 }, /*  31:  1.0476° */
    {   19666,   10241 }, /*  32:  1.0368° */
    {   19340,   10305 }, /*  33:  1.0260° */
    {   19019,   10369 }, /*  34:  1.0152° */
    {   18701,   10433 }, /*  35:  1.0044° */
    {   18388,   10497 }, /*  36:  0.9936° */
    {   18078,   10561 }, /*  37:  0.9828° */
    {   17771,   10625 }, /*  38:  0.9720° */
    {   17469,   10689 }, /*  39:  0.9612° */
    {   17170,   10753 }, /*  40:  0.9504° */
    {   16875,   10817 }, /*  41:  0.9396° */
    {   16583,   10881 }, /*  42:  0.9288° */
    {   16294,   10945 }, /*  43:  0.9180° */
    {   16009,   11009 }, /*  44:  0.9072° */
    {   15727,   11073 }, /*  45:  0.8964° */
    {   15448,   11137 }, /*  46:  0.8856° */
    {   15173,   11201 }, /*  47:  0.8748° */
    {   14901,   11265 }, /*  48:  0.8640° */
    {   14631,   11329 }, /*  49:  0.8532° */
    {   14365,   11393 }, /*  50:  0.8424° */
    {   14102,   11457 }, /*  51:  0.8316° */
    {   13841,   11521 }, /*  52:  0.8208° */
    {   13584,   11585 }, /*  53:  0.8100° */
    {   13329,   11649 }, /*  54:  0.7992° */
    {   13077,   11713 }, /*  55:  0.7884° */
    {   12828,   11777 }, /*  56:  0.7776° */
    {   12582,   11841 }, /*  57:  0.7668° */
    {   12338,   11905 }, /*  58:  0.7560° */
    {   12097,   11969 }, /*  59:  0.7452° */
    {   11858,   12032 }, /*  60:  0.7344° */
    {   11622,   12096 }, /*  61:  0.7236° */
    {   11389,   12160 }, /*  62:  0.7128° */
    {   11157,   12224 }, /*  63:  0.7020° */
    {   10929,   12288 }, /*  64:  0.6912° */
    {   10702,   12352 }, /*  65:  0.6804° */
    {   10478,   12416 }, /*  66:  0.6696° */
    {   10256,   12480 }, /*  67:  0.6588° */
    {   10037,   12544 }, /*  68:  0.6480° */
    {    9819,   12608 }, /*  69:  0.6372° */
    {    9604,   12672 }, /*  70:  0.6264° */
    {    9391,   12736 }, /*  71:  0.6156° */
    {    9181,   12800 }, /*  72:  0.6048° */
    {    8972,   12864 }, /*  73:  0.5940° */
    {    8765,   12928 }, /*  74:  0.5832° */
    {    8561,   12992 }, /*  75:  0.5724° */
    {    8358,   13056 }, /*  76:  0.5616° */
    {    8157,   13120 }, /*  77:  0.5508° */
    {    7959,   13184 }, /*  78:  0.5400° */
    {    7762,   13248 }, /*  79:  0.5292° */
    {    7567,   13312 }, /*  80:  0.5184° */
    {    7374,   13376 }, /*  81:  0.5076° */
    {    7183,   13440 }, /*  82:  0.4968° */
    {    6993,   13504 }, /*  83:  0.4860° */
    {    6806,   13568 }, /*  84:  0.4752° */
    {    6620,   13632 }, /*  85:  0.4644° */
    {    6436,   13696 }, /*  86:  0.4536° */
    {    6253,   13760 }, /*  87:  0.4428° */
    {    6073,   13824 }, /*  88:  0.4320° */
    {    5893,   13888 }, /*  89:  0.4212° */
    {    5716,   13952 }, /*  90:  0.4104° */
    {    5540,   14016 }, /*  91:  0.3996° */
    {    5366,   14080 }, /*  92:  0.3888° */
    {    5193,   14144 }, /*  93:  0.3780° */
    {    5022,   14208 }, /*  94:  0.3672° */
    {    4853,   14272 }, /*  95:  0.3564° */
    {    4685,   14336 }, /*  96:  0.3456° */
    {    4518,   14400 }, /*  97:  0.3348° */
    {    4353,   14464 }, /*  98:  0.3240° */
    {    4189,   14528 }, /*  99:  0.3132° */
    {    4027,   14592 }, /* 100:  0.3024° */
    {    3867,   14656 }, /* 101:  0.2916° */
    {    3707,   14720 }, /* 102:  0.2808° */
    {    3549,   14784 }, /* 103:  0.2700° */
    {    3393,   14848 }, /* 104:  0.2592° */
    {    3237,   14912 }, /* 105:  0.2484° */
    {    3083,   14976 }, /* 106:  0.2376° */
    {    2931,   15040 }, /* 107:  0.2268° */
    {    2779,   15104 }, /* 108:  0.2160° */
    {    2629,   15168 }, /* 109:  0.2052° */
    {    2480,   15232 }, /* 110:  0.1944° */
    {    2333,   15296 }, /* 111:  0.1836° */
    {    2186,   15360 }, /* 112:  0.1728° */
    {    2041,   15424 }, /* 113:  0.1620° */
    {    1897,   15488 }, /* 114:  0.1512° */
    {    1754,   15552 }, /* 115:  0.1404° */
    {    1613,   15616 }, /* 116:  0.1296° */
    {    1472,   15680 }, /* 117:  0.1188° */
    {    1333,   15744 }, /* 118:  0.1080° */
    {    1195,   15808 }, /* 119:  0.0972° */
    {    1058,   15872 }, /* 120:  0.0864° */
    {     922,   15936 }, /* 121:  0.0756° */
    {     787,   16000 }, /* 122:  0.0648° */
    {     653,   16064 }, /* 123:  0.0540° */
    {     520,   16128 }, /* 124:  0.0432° */
    {     388,   16192 }, /* 125:  0.0324° */
    {     258,   16256 }, /* 126:  0.0216° */
    {     128,   16319 }, /* 127:  0.0108° */
    {       0,   16383 }, /* 128: -0.0000° */
    {    -127,   16447 }, /* 129: -0.0108° */
    {    -254,   16512 }, /* 130: -0.0216° */
    {    -380,   16576 }, /* 131: -0.0324° */
    {    -504,   16640 }, /* 132: -0.0432° */
    {    -628,   16704 }, /* 133: -0.0540° */
    {    -751,   16768 }, /* 134: -0.0648° */
    {    -873,   16832 }, /* 135: -0.0756° */
    {    -994,   16896 }, /* 136: -0.0864° */
    {   -1114,   16960 }, /* 137: -0.0972° */
    {   -1233,   17024 }, /* 138: -0.1080° */
    {   -1351,   17088 }, /* 139: -0.1188° */
    {   -1469,   17152 }, /* 140: -0.1296° */
    {   -1585,   17216 }, /* 141: -0.1404° */
    {   -1701,   17280 }, /* 142: -0.1512° */
    {   -1816,   17344 }, /* 143: -0.1620° */
    {   -1930,   17408 }, /* 144: -0.1728° */
    {   -2043,   17472 }, /* 145: -0.1836° */
    {   -2155,   17536 }, /* 146: -0.1944° */
    {   -2266,   17600 }, /* 147: -0.2052° */
    {   -2377,   17664 }, /* 148: -0.2160° */
    {   -2487,   17728 }, /* 149: -0.2268° */
    {   -2596,   17792 }, /* 150: -0.2376° */
    {   -2705,   17856 }, /* 151: -0.2484° */
    {   -2812,   17920 }, /* 152: -0.2592° */
    {   -2919,   17984 }, /* 153: -0.2700° */
    {   -3025,   18048 }, /* 154: -0.2808° */
    {   -3130,   18112 }, /* 155: -0.2916° */
    {   -3235,   18176 }, /* 156: -0.3024° */
    {   -3339,   18240 }, /* 157: -0.3132° */
    {   -3442,   18304 }, /* 158: -0.3240° */
    {   -3544,   18368 }, /* 159: -0.3348° */
    {   -3646,   18432 }, /* 160: -0.3456° */
    {   -3747,   18496 }, /* 161: -0.3564° */
    {   -3847,   18560 }, /* 162: -0.3672° */
    {   -3947,   18624 }, /* 163: -0.3780° */
    {   -4045,   18688 }, /* 164: -0.3888° */
    {   -4144,   18752 }, /* 165: -0.3996° */
    {   -4241,   18816 }, /* 166: -0.4104° */
    {   -4338,   18880 }, /* 167: -0.4212° */
    {   -4434,   18944 }, /* 168: -0.4320° */
    {   -4530,   19008 }, /* 169: -0.4428° */
    {   -4625,   19072 }, /* 170: -0.4536° */
    {   -4719,   19136 }, /* 171: -0.4644° */
    {   -4813,   19200 }, /* 172: -0.4752° */
    {   -4906,   19264 }, /* 173: -0.4860° */
    {   -4999,   19328 }, /* 174: -0.4968° */
    {   -5090,   19392 }, /* 175: -0.5076° */
    {   -5182,   19456 }, /* 176: -0.5184° */
    {   -5272,   19520 }, /* 177: -0.5292° */
    {   -5362,   19584 }, /* 178: -0.5400° */
    {   -5452,   19648 }, /* 179: -0.5508° */
    {   -5541,   19712 }, /* 180: -0.5616° */
    {   -5629,   19776 }, /* 181: -0.5724° */
    {   -5717,   19841 }, /* 182: -0.5832° */
    {   -5804,   19905 }, /* 183: -0.5940° */
    {   -5891,   19969 }, /* 184: -0.6048° */
    {   -5977,   20033 }, /* 185: -0.6156° */
    {   -6062,   20097 }, /* 186: -0.6264° */
    {   -6147,   20161 }, /* 187: -0.6372° */
    {   -6232,   20225 }, /* 188: -0.6480° */
    {   -6316,   20289 }, /* 189: -0.6588° */
    {   -6399,   20353 }, /* 190: -0.6696° */
    {   -6482,   20417 }, /* 191: -0.6804° */
    {   -6565,   20481 }, /* 192: -0.6912° */
    {   -6646,   20545 }, /* 193: -0.7020° */
    {   -6728,   20609 }, /* 194: -0.7128° */
    {   -6809,   20673 }, /* 195: -0.7236° */
    {   -6889,   20737 }, /* 196: -0.7344° */
    {   -6969,   20801 }, /* 197: -0.7452° */
    {   -7048,   20865 }, /* 198: -0.7560° */
    {   -7127,   20929 }, /* 199: -0.7668° */
    {   -7206,   20993 }, /* 200: -0.7776° */
    {   -7284,   21058 }, /* 201: -0.7884° */
    {   -7361,   21122 }, /* 202: -0.7992° */
    {   -7438,   21186 }, /* 203: -0.8100° */
    {   -7515,   21250 }, /* 204: -0.8208° */
    {   -7591,   21314 }, /* 205: -0.8316° */
    {   -7666,   21378 }, /* 206: -0.8424° */
    {   -7741,   21442 }, /* 207: -0.8532° */
    {   -7816,   21506 }, /* 208: -0.8640° */
    {   -7891,   21570 }, /* 209: -0.8748° */
    {   -7964,   21634 }, /* 210: -0.8856° */
    {   -8038,   21698 }, /* 211: -0.8964° */
    {   -8111,   21762 }, /* 212: -0.9072° */
    {   -8183,   21826 }, /* 213: -0.9180° */
    {   -8255,   21890 }, /* 214: -0.9288° */
    {   -8327,   21954 }, /* 215: -0.9396° */
    {   -8399,   22019 }, /* 216: -0.9504° */
    {   -8469,   22083 }, /* 217: -0.9612° */
    {   -8540,   22147 }, /* 218: -0.9720° */
    {   -8610,   22211 }, /* 219: -0.9828° */
    {   -8680,   22275 }, /* 220: -0.9936° */
    {   -8749,   22339 }, /* 221: -1.0044° */
    {   -8818,   22403 }, /* 222: -1.0152° */
    {   -8886,   22467 }, /* 223: -1.0260° */
    {   -8954,   22531 }, /* 224: -1.0368° */
    {   -9022,   22595 }, /* 225: -1.0476° */
    {   -9090,   22659 }, /* 226: -1.0584° */
    {   -9157,   22723 }, /* 227: -1.0692° */
    {   -9223,   22788 }, /* 228: -1.0800° */
    {   -9289,   22852 }, /* 229: -1.0908° */
    {   -9355,   22916 }, /* 230: -1.1016° */
    {   -9421,   22980 }, /* 231: -1.1124° */
    {   -9486,   23044 }, /* 232: -1.1232° */
    {   -9551,   23108 }, /* 233: -1.1340° */
    {   -9615,   23172 }, /* 234: -1.1448° */
    {   -9679,   23236 }, /* 235: -1.1556° */
    {   -9743,   23300 }, /* 236: -1.1664° */
    {   -9806,   23364 }, /* 237: -1.1772° */
    {   -9869,   23429 }, /* 238: -1.1880° */
    {   -9932,   23493 }, /* 239: -1.1988° */
    {   -9994,   23557 }, /* 240: -1.2096° */
    {  -10056,   23621 }, /* 241: -1.2204° */
    {  -10118,   23685 }, /* 242: -1.2312° */
    {  -10179,   23749 }, /* 243: -1.2420° */
    {  -10240,   23813 }, /* 244: -1.2528° */
    {  -10301,   23877 }, /* 245: -1.2636° */
    {  -10361,   23941 }, /* 246: -1.2744° */
    {  -10421,   24006 }, /* 247: -1.2852° */
    {  -10481,   24070 }, /* 248: -1.2960° */
    {  -10540,   24134 }, /* 249: -1.3068° */
    {  -10600,   24198 }, /* 250: -1.3176° */
    {  -10658,   24262 }, /* 251: -1.3284° */
    {  -10717,   24326 }, /* 252: -1.3392° */
    {  -10775,   24390 }, /* 253: -1.3500° */
    {  -10833,   24454 }, /* 254: -1.3608° */
    {  -10890,   24519 }  /* 255: -1.3716 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 80000 && MAINS_NOMINAL_FREQUENCY == 50 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 50 Hz
 *        and sampling 8000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8193 }, /*   0:  1.1250° */
    {   32260,    8257 }, /*   1:  1.1162° */
    {   31760,    8321 }, /*   2:  1.1074° */
    {   31267,    8385 }, /*   3:  1.0986° */
    {   30782,    8449 }, /*   4:  1.0898° */
    {   30305,    8513 }, /*   5:  1.0811° */
    {   29834,    8577 }, /*   6:  1.0723° */
    {   29371,    8641 }, /*   7:  1.0635° */
    {   28914,    8705 }, /*   8:  1.0547° */
    {   28464,    8769 }, /*   9:  1.0459° */
    {   28020,    8833 }, /*  10:  1.0371° */
    {   27583,    8897 }, /*  11:  1.0283° */
    {   27152,    8961 }, /*  12:  1.0195° */
    {   26727,    9025 }, /*  13:  1.0107° */
    {   26308,    9089 }, /*  14:  1.0020° */
    {   25895,    9153 }, /*  15:  0.9932° */
    {   25488,    9217 }, /*  16:  0.9844° */
    {   25087,    9281 }, /*  17:  0.9756° */
    {   24690,    9345 }, /*  18:  0.9668° */
    {   24300,    9409 }, /*  19:  0.9580° */
    {   23914,    9473 }, /*  20:  0.9492° */
    {   23534,    9537 }, /*  21:  0.9404° */
    {   23159,    9601 }, /*  22:  0.9316° */
    {   22788,    9665 }, /*  23:  0.9229° */
    {   22423,    9729 }, /*  24:  0.9141° */
    {   22062,    9793 }, /*  25:  0.9053° */
    {   21706,    9857 }, /*  26:  0.8965° */
    {   21355,    9921 }, /*  27:  0.8877° */
    {   21008,    9985 }, /*  28:  0.8789° */
    {   20666,   10049 }, /*  29:  0.8701° */
    {   20328,   10113 }, /*  30:  0.8613° */
    {   19994,   10177 }, /*  31:  0.8525° */
    {   19664,   10241 }, /*  32:  0.8437° */
    {   19338,   10305 }, /*  33:  0.8350° */
    {   19017,   10369 }, /*  34:  0.8262° */
    {   18699,   10433 }, /*  35:  0.8174° */
    {   18386,   10497 }, /*  36:  0.8086° */
    {   18076,   10561 }, /*  37:  0.7998° */
    {   17769,   10625 }, /*  38:  0.7910° */
    {   17467,   10688 }, /*  39:  0.7822° */
    {   17168,   10752 }, /*  40:  0.7734° */
    {   16872,   10816 }, /*  41:  0.7646° */
    {   16580,   10880 }, /*  42:  0.7559° */
    {   16292,   10944 }, /*  43:  0.7471° */
    {   16007,   11008 }, /*  44:  0.7383° */
    {   15725,   11072 }, /*  45:  0.7295° */
    {   15446,   11136 }, /*  46:  0.7207° */
    {   15171,   11200 }, /*  47:  0.7119° */
    {   14898,   11264 }, /*  48:  0.7031° */
    {   14629,   11328 }, /*  49:  0.6943° */
    {   14363,   11392 }, /*  50:  0.6855° */
    {   14100,   11456 }, /*  51:  0.6768° */
    {   13839,   11520 }, /*  52:  0.6680° */
    {   13582,   11584 }, /*  53:  0.6592° */
    {   13327,   11648 }, /*  54:  0.6504° */
    {   13075,   11712 }, /*  55:  0.6416° */
    {   12826,   11776 }, /*  56:  0.6328° */
    {   12580,   11840 }, /*  57:  0.6240° */
    {   12336,   11904 }, /*  58:  0.6152° */
    {   12095,   11968 }, /*  59:  0.6064° */
    {   11856,   12032 }, /*  60:  0.5977° */
    {   11620,   12096 }, /*  61:  0.5889° */
    {   11386,   12160 }, /*  62:  0.5801° */
    {   11155,   12224 }, /*  63:  0.5713° */
    {   10926,   12288 }, /*  64:  0.5625° */
    {   10700,   12352 }, /*  65:  0.5537° */
    {   10476,   12416 }, /*  66:  0.5449° */
    {   10254,   12480 }, /*  67:  0.5361° */
    {   10035,   12544 }, /*  68:  0.5273° */
    {    9817,   12608 }, /*  69:  0.5186° */
    {    9602,   12672 }, /*  70:  0.5098° */
    {    9389,   12736 }, /*  71:  0.5010° */
    {    9179,   12800 }, /*  72:  0.4922° */
    {    8970,   12864 }, /*  73:  0.4834° */
    {    8763,   12928 }, /*  74:  0.4746° */
    {    8559,   12992 }, /*  75:  0.4658° */
    {    8356,   13056 }, /*  76:  0.4570° */
    {    8155,   13120 }, /*  77:  0.4482° */
    {    7957,   13184 }, /*  78:  0.4395° */
    {    7760,   13248 }, /*  79:  0.4307° */
    {    7565,   13312 }, /*  80:  0.4219° */
    {    7372,   13376 }, /*  81:  0.4131° */
    {    7181,   13440 }, /*  82:  0.4043° */
    {    6991,   13504 }, /*  83:  0.3955° */
    {    6804,   13568 }, /*  84:  0.3867° */
    {    6618,   13632 }, /*  85:  0.3779° */
    {    6434,   13696 }, /*  86:  0.3691° */
    {    6252,   13760 }, /*  87:  0.3604° */
    {    6071,   13824 }, /*  88:  0.3516° */
    {    5892,   13888 }, /*  89:  0.3428° */
    {    5714,   13952 }, /*  90:  0.3340° */
    {    5539,   14016 }, /*  91:  0.3252° */
    {    5365,   14080 }, /*  92:  0.3164° */
    {    5192,   14144 }, /*  93:  0.3076° */
    {    5021,   14208 }, /*  94:  0.2988° */
    {    4851,   14272 }, /*  95:  0.2900° */
    {    4683,   14336 }, /*  96:  0.2812° */
    {    4517,   14400 }, /*  97:  0.2725° */
    {    4352,   14464 }, /*  98:  0.2637° */
    {    4188,   14528 }, /*  99:  0.2549° */
    {    4026,   14592 }, /* 100:  0.2461° */
    {    3865,   14656 }, /* 101:  0.2373° */
    {    3706,   14720 }, /* 102:  0.2285° */
    {    3548,   14784 }, /* 103:  0.2197° */
    {    3391,   14848 }, /* 104:  0.2109° */
    {    3236,   14912 }, /* 105:  0.2021° */
    {    3082,   14976 }, /* 106:  0.1934° */
    {    2930,   15040 }, /* 107:  0.1846° */
    {    2778,   15104 }, /* 108:  0.1758° */
    {    2628,   15168 }, /* 109:  0.1670° */
    {    2479,   15232 }, /* 110:  0.1582° */
    {    2332,   15296 }, /* 111:  0.1494° */
    {    2186,   15360 }, /* 112:  0.1406° */
    {    2040,   15424 }, /* 113:  0.1318° */
    {    1896,   15488 }, /* 114:  0.1230° */
    {    1754,   15552 }, /* 115:  0.1143° */
    {    1612,   15616 }, /* 116:  0.1055° */
    {    1472,   15680 }, /* 117:  0.0967° */
    {    1332,   15744 }, /* 118:  0.0879° */
    {    1194,   15808 }, /* 119:  0.0791° */
    {    1057,   15872 }, /* 120:  0.0703° */
    {     921,   15936 }, /* 121:  0.0615° */
    {     787,   16000 }, /* 122:  0.0527° */
    {     653,   16064 }, /* 123:  0.0439° */
    {     520,   16128 }, /* 124:  0.0352° */
    {     388,   16192 }, /* 125:  0.0264° */
    {     258,   16256 }, /* 126:  0.0176° */
    {     128,   16320 }, /* 127:  0.0088° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0088° */
    {    -254,   16512 }, /* 130: -0.0176° */
    {    -379,   16576 }, /* 131: -0.0264° */
    {    -504,   16640 }, /* 132: -0.0352° */
    {    -628,   16704 }, /* 133: -0.0439° */
    {    -751,   16768 }, /* 134: -0.0527° */
    {    -872,   16832 }, /* 135: -0.0615° */
    {    -993,   16896 }, /* 136: -0.0703° */
    {   -1113,   16960 }, /* 137: -0.0791° */
    {   -1232,   17024 }, /* 138: -0.0879° */
    {   -1351,   17088 }, /* 139: -0.0967° */
    {   -1468,   17152 }, /* 140: -0.1055° */
    {   -1584,   17216 }, /* 141: -0.1143° */
    {   -1700,   17280 }, /* 142: -0.1230° */
    {   -1815,   17344 }, /* 143: -0.1318° */
    {   -1929,   17408 }, /* 144: -0.1406° */
    {   -2042,   17472 }, /* 145: -0.1494° */
    {   -2154,   17536 }, /* 146: -0.1582° */
    {   -2265,   17600 }, /* 147: -0.1670° */
    {   -2376,   17664 }, /* 148: -0.1758° */
    {   -2486,   17728 }, /* 149: -0.1846° */
    {   -2595,   17792 }, /* 150: -0.1934° */
    {   -2703,   17856 }, /* 151: -0.2021° */
    {   -2811,   17920 }, /* 152: -0.2109° */
    {   -2917,   17984 }, /* 153: -0.2197° */
    {   -3023,   18048 }, /* 154: -0.2285° */
    {   -3129,   18112 }, /* 155: -0.2373° */
    {   -3233,   18176 }, /* 156: -0.2461° */
    {   -3337,   18240 }, /* 157: -0.2549° */
    {   -3440,   18304 }, /* 158: -0.2637° */
    {   -3542,   18368 }, /* 159: -0.2725° */
    {   -3644,   18432 }, /* 160: -0.2813° */
    {   -3745,   18496 }, /* 161: -0.2900° */
    {   -3845,   18560 }, /* 162: -0.2988° */
    {   -3945,   18624 }, /* 163: -0.3076° */
    {   -4043,   18688 }, /* 164: -0.3164° */
    {   -4142,   18752 }, /* 165: -0.3252° */
    {   -4239,   18816 }, /* 166: -0.3340° */
    {   -4336,   18880 }, /* 167: -0.3428° */
    {   -4432,   18944 }, /* 168: -0.3516° */
    {   -4528,   19008 }, /* 169: -0.3604° */
    {   -4623,   19072 }, /* 170: -0.3691° */
    {   -4717,   19136 }, /* 171: -0.3779° */
    {   -4810,   19200 }, /* 172: -0.3867° */
    {   -4903,   19264 }, /* 173: -0.3955° */
    {   -4996,   19328 }, /* 174: -0.4043° */
    {   -5088,   19392 }, /* 175: -0.4131° */
    {   -5179,   19456 }, /* 176: -0.4219° */
    {   -5269,   19520 }, /* 177: -0.4307° */
    {   -5360,   19584 }, /* 178: -0.4395° */
    {   -5449,   19648 }, /* 179: -0.4482° */
    {   -5538,   19712 }, /* 180: -0.4570° */
    {   -5626,   19776 }, /* 181: -0.4658° */
    {   -5714,   19840 }, /* 182: -0.4746° */
    {   -5801,   19904 }, /* 183: -0.4834° */
    {   -5887,   19968 }, /* 184: -0.4922° */
    {   -5973,   20032 }, /* 185: -0.5010° */
    {   -6059,   20096 }, /* 186: -0.5098° */
    {   -6144,   20160 }, /* 187: -0.5186° */
    {   -6228,   20224 }, /* 188: -0.5273° */
    {   -6312,   20288 }, /* 189: -0.5361° */
    {   -6396,   20352 }, /* 190: -0.5449° */
    {   -6478,   20416 }, /* 191: -0.5537° */
    {   -6561,   20480 }, /* 192: -0.5625° */
    {   -6642,   20545 }, /* 193: -0.5713° */
    {   -6724,   20609 }, /* 194: -0.5801° */
    {   -6805,   20673 }, /* 195: -0.5889° */
    {   -6885,   20737 }, /* 196: -0.5977° */
    {   -6965,   20801 }, /* 197: -0.6064° */
    {   -7044,   20865 }, /* 198: -0.6152° */
    {   -7123,   20929 }, /* 199: -0.6240° */
    {   -7201,   20993 }, /* 200: -0.6328° */
    {   -7279,   21057 }, /* 201: -0.6416° */
    {   -7356,   21121 }, /* 202: -0.6504° */
    {   -7433,   21185 }, /* 203: -0.6592° */
    {   -7510,   21249 }, /* 204: -0.6680° */
    {   -7586,   21313 }, /* 205: -0.6768° */
    {   -7661,   21377 }, /* 206: -0.6855° */
    {   -7737,   21441 }, /* 207: -0.6943° */
    {   -7811,   21505 }, /* 208: -0.7031° */
    {   -7885,   21569 }, /* 209: -0.7119° */
    {   -7959,   21633 }, /* 210: -0.7207° */
    {   -8033,   21697 }, /* 211: -0.7295° */
    {   -8105,   21761 }, /* 212: -0.7383° */
    {   -8178,   21825 }, /* 213: -0.7471° */
    {   -8250,   21889 }, /* 214: -0.7559° */
    {   -8322,   21953 }, /* 215: -0.7646° */
    {   -8393,   22018 }, /* 216: -0.7734° */
    {   -8464,   22082 }, /* 217: -0.7822° */
    {   -8534,   22146 }, /* 218: -0.7910° */
    {   -8604,   22210 }, /* 219: -0.7998° */
    {   -8674,   22274 }, /* 220: -0.8086° */
    {   -8743,   22338 }, /* 221: -0.8174° */
    {   -8812,   22402 }, /* 222: -0.8262° */
    {   -8880,   22466 }, /* 223: -0.8350° */
    {   -8948,   22530 }, /* 224: -0.8438° */
    {   -9016,   22594 }, /* 225: -0.8525° */
    {   -9083,   22658 }, /* 226: -0.8613° */
    {   -9150,   22722 }, /* 227: -0.8701° */
    {   -9217,   22786 }, /* 228: -0.8789° */
    {   -9283,   22850 }, /* 229: -0.8877° */
    {   -9349,   22914 }, /* 230: -0.8965° */
    {   -9414,   22978 }, /* 231: -0.9053° */
    {   -9479,   23042 }, /* 232: -0.9141° */
    {   -9544,   23107 }, /* 233: -0.9229° */
    {   -9608,   23171 }, /* 234: -0.9316° */
    {   -9672,   23235 }, /* 235: -0.9404° */
    {   -9736,   23299 }, /* 236: -0.9492° */
    {   -9799,   23363 }, /* 237: -0.9580° */
    {   -9862,   23427 }, /* 238: -0.9668° */
    {   -9925,   23491 }, /* 239: -0.9756° */
    {   -9987,   23555 }, /* 240: -0.9844° */
    {  -10049,   23619 }, /* 241: -0.9932° */
    {  -10110,   23683 }, /* 242: -1.0020° */
    {  -10172,   23747 }, /* 243: -1.0107° */
    {  -10233,   23811 }, /* 244: -1.0195° */
    {  -10293,   23875 }, /* 245: -1.0283° */
    {  -10353,   23939 }, /* 246: -1.0371° */
    {  -10413,   24004 }, /* 247: -1.0459° */
    {  -10473,   24068 }, /* 248: -1.0547° */
    {  -10532,   24132 }, /* 249: -1.0635° */
    {  -10591,   24196 }, /* 250: -1.0723° */
    {  -10650,   24260 }, /* 251: -1.0811° */
    {  -10708,   24324 }, /* 252: -1.0898° */
    {  -10767,   24388 }, /* 253: -1.0986° */
    {  -10824,   24452 }, /* 254: -1.1074° */
    {  -10882,   24516 }  /* 255: -1.1162 deg */
};




#elif INT_SAMPLES_PER_10_SECONDS == 80000 && MAINS_NOMINAL_FREQUENCY == 60 && PHASE_SHIFT_FIR_TABLE_ELEMENTS == 256
/**
 *
 * @brief FIR coefficients to correct specific phase shift angles at 60 Hz
 *        and sampling 8000 times per second. There are 256 steps in this table.
 */

const int32_t phase_shift_fir_coeffs[PHASE_SHIFT_FIR_TABLE_ELEMENTS][2] =
{
/*      beta,        gain                   degrees  */
    {   32768,    8194 }, /*   0:  1.3500° */
    {   32260,    8258 }, /*   1:  1.3395° */
    {   31760,    8322 }, /*   2:  1.3289° */
    {   31267,    8386 }, /*   3:  1.3184° */
    {   30783,    8450 }, /*   4:  1.3078° */
    {   30305,    8514 }, /*   5:  1.2973° */
    {   29835,    8578 }, /*   6:  1.2867° */
    {   29371,    8642 }, /*   7:  1.2762° */
    {   28914,    8706 }, /*   8:  1.2656° */
    {   28464,    8770 }, /*   9:  1.2551° */
    {   28021,    8834 }, /*  10:  1.2445° */
    {   27584,    8898 }, /*  11:  1.2340° */
    {   27153,    8962 }, /*  12:  1.2234° */
    {   26728,    9026 }, /*  13:  1.2129° */
    {   26309,    9090 }, /*  14:  1.2023° */
    {   25896,    9153 }, /*  15:  1.1918° */
    {   25489,    9217 }, /*  16:  1.1812° */
    {   25088,    9281 }, /*  17:  1.1707° */
    {   24692,    9345 }, /*  18:  1.1602° */
    {   24301,    9409 }, /*  19:  1.1496° */
    {   23915,    9473 }, /*  20:  1.1391° */
    {   23535,    9537 }, /*  21:  1.1285° */
    {   23160,    9601 }, /*  22:  1.1180° */
    {   22790,    9665 }, /*  23:  1.1074° */
    {   22424,    9729 }, /*  24:  1.0969° */
    {   22064,    9793 }, /*  25:  1.0863° */
    {   21708,    9857 }, /*  26:  1.0758° */
    {   21357,    9921 }, /*  27:  1.0652° */
    {   21010,    9985 }, /*  28:  1.0547° */
    {   20667,   10049 }, /*  29:  1.0441° */
    {   20329,   10113 }, /*  30:  1.0336° */
    {   19995,   10177 }, /*  31:  1.0230° */
    {   19666,   10241 }, /*  32:  1.0125° */
    {   19340,   10305 }, /*  33:  1.0020° */
    {   19019,   10369 }, /*  34:  0.9914° */
    {   18701,   10433 }, /*  35:  0.9809° */
    {   18387,   10497 }, /*  36:  0.9703° */
    {   18077,   10561 }, /*  37:  0.9598° */
    {   17771,   10625 }, /*  38:  0.9492° */
    {   17469,   10689 }, /*  39:  0.9387° */
    {   17170,   10753 }, /*  40:  0.9281° */
    {   16874,   10817 }, /*  41:  0.9176° */
    {   16582,   10881 }, /*  42:  0.9070° */
    {   16294,   10945 }, /*  43:  0.8965° */
    {   16009,   11009 }, /*  44:  0.8859° */
    {   15727,   11073 }, /*  45:  0.8754° */
    {   15448,   11137 }, /*  46:  0.8648° */
    {   15173,   11201 }, /*  47:  0.8543° */
    {   14900,   11265 }, /*  48:  0.8437° */
    {   14631,   11329 }, /*  49:  0.8332° */
    {   14365,   11393 }, /*  50:  0.8227° */
    {   14101,   11457 }, /*  51:  0.8121° */
    {   13841,   11521 }, /*  52:  0.8016° */
    {   13584,   11585 }, /*  53:  0.7910° */
    {   13329,   11649 }, /*  54:  0.7805° */
    {   13077,   11713 }, /*  55:  0.7699° */
    {   12828,   11777 }, /*  56:  0.7594° */
    {   12582,   11841 }, /*  57:  0.7488° */
    {   12338,   11904 }, /*  58:  0.7383° */
    {   12097,   11968 }, /*  59:  0.7277° */
    {   11858,   12032 }, /*  60:  0.7172° */
    {   11622,   12096 }, /*  61:  0.7066° */
    {   11388,   12160 }, /*  62:  0.6961° */
    {   11157,   12224 }, /*  63:  0.6855° */
    {   10928,   12288 }, /*  64:  0.6750° */
    {   10702,   12352 }, /*  65:  0.6645° */
    {   10478,   12416 }, /*  66:  0.6539° */
    {   10256,   12480 }, /*  67:  0.6434° */
    {   10036,   12544 }, /*  68:  0.6328° */
    {    9819,   12608 }, /*  69:  0.6223° */
    {    9604,   12672 }, /*  70:  0.6117° */
    {    9391,   12736 }, /*  71:  0.6012° */
    {    9180,   12800 }, /*  72:  0.5906° */
    {    8972,   12864 }, /*  73:  0.5801° */
    {    8765,   12928 }, /*  74:  0.5695° */
    {    8560,   12992 }, /*  75:  0.5590° */
    {    8358,   13056 }, /*  76:  0.5484° */
    {    8157,   13120 }, /*  77:  0.5379° */
    {    7958,   13184 }, /*  78:  0.5273° */
    {    7761,   13248 }, /*  79:  0.5168° */
    {    7567,   13312 }, /*  80:  0.5062° */
    {    7374,   13376 }, /*  81:  0.4957° */
    {    7182,   13440 }, /*  82:  0.4852° */
    {    6993,   13504 }, /*  83:  0.4746° */
    {    6805,   13568 }, /*  84:  0.4641° */
    {    6620,   13632 }, /*  85:  0.4535° */
    {    6435,   13696 }, /*  86:  0.4430° */
    {    6253,   13760 }, /*  87:  0.4324° */
    {    6072,   13824 }, /*  88:  0.4219° */
    {    5893,   13888 }, /*  89:  0.4113° */
    {    5716,   13952 }, /*  90:  0.4008° */
    {    5540,   14016 }, /*  91:  0.3902° */
    {    5366,   14080 }, /*  92:  0.3797° */
    {    5193,   14144 }, /*  93:  0.3691° */
    {    5022,   14208 }, /*  94:  0.3586° */
    {    4853,   14272 }, /*  95:  0.3480° */
    {    4685,   14336 }, /*  96:  0.3375° */
    {    4518,   14400 }, /*  97:  0.3270° */
    {    4353,   14464 }, /*  98:  0.3164° */
    {    4189,   14528 }, /*  99:  0.3059° */
    {    4027,   14592 }, /* 100:  0.2953° */
    {    3866,   14656 }, /* 101:  0.2848° */
    {    3707,   14720 }, /* 102:  0.2742° */
    {    3549,   14784 }, /* 103:  0.2637° */
    {    3392,   14848 }, /* 104:  0.2531° */
    {    3237,   14912 }, /* 105:  0.2426° */
    {    3083,   14976 }, /* 106:  0.2320° */
    {    2930,   15040 }, /* 107:  0.2215° */
    {    2779,   15104 }, /* 108:  0.2109° */
    {    2629,   15168 }, /* 109:  0.2004° */
    {    2480,   15232 }, /* 110:  0.1898° */
    {    2333,   15296 }, /* 111:  0.1793° */
    {    2186,   15360 }, /* 112:  0.1687° */
    {    2041,   15424 }, /* 113:  0.1582° */
    {    1897,   15488 }, /* 114:  0.1477° */
    {    1754,   15552 }, /* 115:  0.1371° */
    {    1613,   15616 }, /* 116:  0.1266° */
    {    1472,   15680 }, /* 117:  0.1160° */
    {    1333,   15744 }, /* 118:  0.1055° */
    {    1195,   15808 }, /* 119:  0.0949° */
    {    1058,   15872 }, /* 120:  0.0844° */
    {     922,   15936 }, /* 121:  0.0738° */
    {     787,   16000 }, /* 122:  0.0633° */
    {     653,   16064 }, /* 123:  0.0527° */
    {     520,   16128 }, /* 124:  0.0422° */
    {     388,   16192 }, /* 125:  0.0316° */
    {     258,   16256 }, /* 126:  0.0211° */
    {     128,   16320 }, /* 127:  0.0105° */
    {       0,   16384 }, /* 128: -0.0000° */
    {    -127,   16448 }, /* 129: -0.0105° */
    {    -254,   16512 }, /* 130: -0.0211° */
    {    -379,   16576 }, /* 131: -0.0316° */
    {    -504,   16640 }, /* 132: -0.0422° */
    {    -628,   16704 }, /* 133: -0.0527° */
    {    -751,   16768 }, /* 134: -0.0633° */
    {    -873,   16832 }, /* 135: -0.0738° */
    {    -994,   16896 }, /* 136: -0.0844° */
    {   -1114,   16960 }, /* 137: -0.0949° */
    {   -1233,   17024 }, /* 138: -0.1055° */
    {   -1351,   17088 }, /* 139: -0.1160° */
    {   -1469,   17152 }, /* 140: -0.1266° */
    {   -1585,   17216 }, /* 141: -0.1371° */
    {   -1701,   17280 }, /* 142: -0.1477° */
    {   -1815,   17344 }, /* 143: -0.1582° */
    {   -1929,   17408 }, /* 144: -0.1688° */
    {   -2043,   17472 }, /* 145: -0.1793° */
    {   -2155,   17536 }, /* 146: -0.1898° */
    {   -2266,   17600 }, /* 147: -0.2004° */
    {   -2377,   17664 }, /* 148: -0.2109° */
    {   -2487,   17728 }, /* 149: -0.2215° */
    {   -2596,   17792 }, /* 150: -0.2320° */
    {   -2704,   17856 }, /* 151: -0.2426° */
    {   -2812,   17920 }, /* 152: -0.2531° */
    {   -2919,   17984 }, /* 153: -0.2637° */
    {   -3025,   18048 }, /* 154: -0.2742° */
    {   -3130,   18112 }, /* 155: -0.2848° */
    {   -3235,   18176 }, /* 156: -0.2953° */
    {   -3338,   18240 }, /* 157: -0.3059° */
    {   -3441,   18304 }, /* 158: -0.3164° */
    {   -3544,   18368 }, /* 159: -0.3270° */
    {   -3645,   18432 }, /* 160: -0.3375° */
    {   -3746,   18496 }, /* 161: -0.3480° */
    {   -3847,   18560 }, /* 162: -0.3586° */
    {   -3946,   18624 }, /* 163: -0.3691° */
    {   -4045,   18688 }, /* 164: -0.3797° */
    {   -4143,   18752 }, /* 165: -0.3902° */
    {   -4241,   18816 }, /* 166: -0.4008° */
    {   -4338,   18880 }, /* 167: -0.4113° */
    {   -4434,   18944 }, /* 168: -0.4219° */
    {   -4530,   19008 }, /* 169: -0.4324° */
    {   -4625,   19072 }, /* 170: -0.4430° */
    {   -4719,   19136 }, /* 171: -0.4535° */
    {   -4813,   19200 }, /* 172: -0.4641° */
    {   -4906,   19264 }, /* 173: -0.4746° */
    {   -4998,   19328 }, /* 174: -0.4852° */
    {   -5090,   19392 }, /* 175: -0.4957° */
    {   -5181,   19456 }, /* 176: -0.5063° */
    {   -5272,   19520 }, /* 177: -0.5168° */
    {   -5362,   19584 }, /* 178: -0.5273° */
    {   -5452,   19648 }, /* 179: -0.5379° */
    {   -5540,   19712 }, /* 180: -0.5484° */
    {   -5629,   19776 }, /* 181: -0.5590° */
    {   -5717,   19840 }, /* 182: -0.5695° */
    {   -5804,   19905 }, /* 183: -0.5801° */
    {   -5890,   19969 }, /* 184: -0.5906° */
    {   -5976,   20033 }, /* 185: -0.6012° */
    {   -6062,   20097 }, /* 186: -0.6117° */
    {   -6147,   20161 }, /* 187: -0.6223° */
    {   -6231,   20225 }, /* 188: -0.6328° */
    {   -6315,   20289 }, /* 189: -0.6434° */
    {   -6399,   20353 }, /* 190: -0.6539° */
    {   -6482,   20417 }, /* 191: -0.6645° */
    {   -6564,   20481 }, /* 192: -0.6750° */
    {   -6646,   20545 }, /* 193: -0.6855° */
    {   -6727,   20609 }, /* 194: -0.6961° */
    {   -6808,   20673 }, /* 195: -0.7066° */
    {   -6888,   20737 }, /* 196: -0.7172° */
    {   -6968,   20801 }, /* 197: -0.7277° */
    {   -7048,   20865 }, /* 198: -0.7383° */
    {   -7127,   20929 }, /* 199: -0.7488° */
    {   -7205,   20993 }, /* 200: -0.7594° */
    {   -7283,   21057 }, /* 201: -0.7699° */
    {   -7360,   21121 }, /* 202: -0.7805° */
    {   -7437,   21186 }, /* 203: -0.7910° */
    {   -7514,   21250 }, /* 204: -0.8016° */
    {   -7590,   21314 }, /* 205: -0.8121° */
    {   -7666,   21378 }, /* 206: -0.8227° */
    {   -7741,   21442 }, /* 207: -0.8332° */
    {   -7816,   21506 }, /* 208: -0.8438° */
    {   -7890,   21570 }, /* 209: -0.8543° */
    {   -7964,   21634 }, /* 210: -0.8648° */
    {   -8037,   21698 }, /* 211: -0.8754° */
    {   -8110,   21762 }, /* 212: -0.8859° */
    {   -8183,   21826 }, /* 213: -0.8965° */
    {   -8255,   21890 }, /* 214: -0.9070° */
    {   -8326,   21954 }, /* 215: -0.9176° */
    {   -8398,   22018 }, /* 216: -0.9281° */
    {   -8469,   22082 }, /* 217: -0.9387° */
    {   -8539,   22147 }, /* 218: -0.9492° */
    {   -8609,   22211 }, /* 219: -0.9598° */
    {   -8679,   22275 }, /* 220: -0.9703° */
    {   -8748,   22339 }, /* 221: -0.9809° */
    {   -8817,   22403 }, /* 222: -0.9914° */
    {   -8886,   22467 }, /* 223: -1.0020° */
    {   -8954,   22531 }, /* 224: -1.0125° */
    {   -9021,   22595 }, /* 225: -1.0230° */
    {   -9089,   22659 }, /* 226: -1.0336° */
    {   -9156,   22723 }, /* 227: -1.0441° */
    {   -9222,   22787 }, /* 228: -1.0547° */
    {   -9288,   22851 }, /* 229: -1.0652° */
    {   -9354,   22916 }, /* 230: -1.0758° */
    {   -9420,   22980 }, /* 231: -1.0863° */
    {   -9485,   23044 }, /* 232: -1.0969° */
    {   -9550,   23108 }, /* 233: -1.1074° */
    {   -9614,   23172 }, /* 234: -1.1180° */
    {   -9678,   23236 }, /* 235: -1.1285° */
    {   -9742,   23300 }, /* 236: -1.1391° */
    {   -9805,   23364 }, /* 237: -1.1496° */
    {   -9868,   23428 }, /* 238: -1.1602° */
    {   -9931,   23492 }, /* 239: -1.1707° */
    {   -9993,   23557 }, /* 240: -1.1813° */
    {  -10055,   23621 }, /* 241: -1.1918° */
    {  -10117,   23685 }, /* 242: -1.2023° */
    {  -10178,   23749 }, /* 243: -1.2129° */
    {  -10239,   23813 }, /* 244: -1.2234° */
    {  -10300,   23877 }, /* 245: -1.2340° */
    {  -10360,   23941 }, /* 246: -1.2445° */
    {  -10420,   24005 }, /* 247: -1.2551° */
    {  -10480,   24069 }, /* 248: -1.2656° */
    {  -10539,   24134 }, /* 249: -1.2762° */
    {  -10598,   24198 }, /* 250: -1.2867° */
    {  -10657,   24262 }, /* 251: -1.2973° */
    {  -10716,   24326 }, /* 252: -1.3078° */
    {  -10774,   24390 }, /* 253: -1.3184° */
    {  -10832,   24454 }, /* 254: -1.3289° */
    {  -10889,   24518 }  /* 255: -1.3395 deg */
};




#endif
