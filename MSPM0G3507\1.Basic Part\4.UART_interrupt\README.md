# MSPM0G3507总线舵机控制系统

## 快速开始

### 硬件要求
- MSPM0G3507开发板
- 2个总线舵机（ID1和ID2）
- 舵机驱动板（支持UART通信）
- 连接线

### 硬件连接
```
MSPM0G3507    <->    舵机驱动板
PA10 (UART_TX) <->   信号输入
PA11 (UART_RX) <->   信号输出
GND            <->   GND
```

### 软件配置
1. 波特率：115200bps
2. 半双工模式
3. 舵机ID：ID1(水平)，ID2(垂直)

## 功能说明

### 按键功能
- **KEY0 (PA18)**：
  - 短按：点亮红色LED
  - 长按：熄灭红色LED

- **KEY1 (PB21)**：
  - 短按：控制舵机运动
    - ID1舵机 → 90度
    - ID2舵机 → 120度
    - 点亮绿色LED表示完成
  - 长按：熄灭绿色LED

### LED指示
- **红色LED (PB26)**：KEY0控制状态
- **绿色LED (PB27)**：舵机控制完成指示
- **蓝色LED (PB22)**：系统运行指示（自动闪烁）

### 串口调试
- 波特率：115200
- 连接串口助手可查看系统状态信息

## 代码结构

```
├── USER/
│   ├── main.c              # 主函数
│   └── A_include.h         # 头文件
├── software/
│   ├── UART/               # 串口通信模块
│   └── Timer/              # 定时器模块
├── hardware/
│   ├── SERVO/              # 舵机控制模块
│   ├── LED/                # LED控制
│   └── KEY/                # 按键检测
└── 配置文件
```

## 主要修改内容

### 1. UART模块升级
- 波特率从9600改为115200
- 添加半双工模式支持
- 新增多字节发送/接收函数

### 2. 新增舵机控制模块
- 实现总线舵机通信协议
- 支持角度控制和位置反馈
- 提供标准API接口

### 3. 主函数逻辑更新
- 添加舵机初始化
- 实现按键控制舵机功能
- 增加串口调试输出

## 使用方法

1. **编译下载**：使用Keil编译并下载到开发板
2. **硬件连接**：按照连接图连接舵机
3. **上电测试**：观察LED闪烁，表示系统正常
4. **功能测试**：
   - 按KEY0测试LED控制
   - 按KEY1测试舵机控制
   - 连接串口查看调试信息

## 技术特点

- **高效通信**：115200bps高速串口通信
- **半双工协议**：适配总线舵机通信要求
- **模块化设计**：便于功能扩展和维护
- **实时响应**：中断驱动，响应迅速
- **调试友好**：丰富的串口调试信息

## 故障排除

### 舵机无响应
1. 检查UART连接
2. 确认舵机ID设置
3. 验证供电是否正常

### 通信异常
1. 检查波特率设置
2. 确认半双工模式
3. 验证指令格式

## 扩展功能

- 支持更多舵机控制
- 添加位置反馈功能
- 实现轨迹规划
- 增加安全保护机制

---

**开发环境**：Keil MDK + TI SysConfig
**目标芯片**：MSPM0G3507
**通信协议**：标准总线舵机协议
**文档版本**：v1.0
