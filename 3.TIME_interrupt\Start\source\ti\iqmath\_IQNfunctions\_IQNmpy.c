/*!****************************************************************************
 *  @file       _IQNmpy.c
 *  @brief      Functions to multiply two values of IQN type.
 *
 *  <hr>
 ******************************************************************************/
#include "_IQNmpy.h"

/**
 * @brief Multiplies two values of IQ31 format.
 *
 * @param a             IQ31 type value to be multiplied.
 * @param b             IQ31 type value to be multiplied.
 *
 * @return              IQ31 type result of the multiplication. 
 */
int32_t _IQ31mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 31);
}
/**
 * @brief Multiplies two values of IQ30 format.
 *
 * @param a             IQ30 type value to be multiplied.
 * @param b             IQ30 type value to be multiplied.
 *
 * @return              IQ30 type result of the multiplication.    
 */
int32_t _IQ30mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 30);
}
/**
 * @brief Multiplies two values of IQ29 format.
 *
 * @param a             IQ29 type value to be multiplied.
 * @param b             IQ29 type value to be multiplied.
 *
 * @return              IQ29 type result of the multiplication.    
 */
int32_t _IQ29mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 29);
}
/**
 * @brief Multiplies two values of IQ28 format.
 *
 * @param a             IQ28 type value to be multiplied.
 * @param b             IQ28 type value to be multiplied.
 *
 * @return              IQ28 type result of the multiplication.    
 */
int32_t _IQ28mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 28);
}
/**
 * @brief Multiplies two values of IQ27 format.
 *
 * @param a             IQ27 type value to be multiplied.
 * @param b             IQ27 type value to be multiplied.
 *
 * @return              IQ27 type result of the multiplication.    
 */
int32_t _IQ27mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 27);
}
/**
 * @brief Multiplies two values of IQ26 format.
 *
 * @param a             IQ26 type value to be multiplied.
 * @param b             IQ26 type value to be multiplied.
 *
 * @return              IQ26 type result of the multiplication.    
 */
int32_t _IQ26mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 26);
}
/**
 * @brief Multiplies two values of IQ25 format.
 *
 * @param a             IQ25 type value to be multiplied.
 * @param b             IQ25 type value to be multiplied.
 *
 * @return              IQ25 type result of the multiplication.    
 */
int32_t _IQ25mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 25);
}
/**
 * @brief Multiplies two values of IQ24 format.
 *
 * @param a             IQ24 type value to be multiplied.
 * @param b             IQ24 type value to be multiplied.
 *
 * @return              IQ24 type result of the multiplication.    
 */
int32_t _IQ24mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 24);
}
/**
 * @brief Multiplies two values of IQ23 format.
 *
 * @param a             IQ23 type value to be multiplied.
 * @param b             IQ23 type value to be multiplied.
 *
 * @return              IQ23 type result of the multiplication.    
 */
int32_t _IQ23mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 23);
}
/**
 * @brief Multiplies two values of IQ22 format.
 *
 * @param a             IQ22 type value to be multiplied.
 * @param b             IQ22 type value to be multiplied.
 *
 * @return              IQ22 type result of the multiplication.    
 */
int32_t _IQ22mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 22);
}
/**
 * @brief Multiplies two values of IQ21 format.
 *
 * @param a             IQ21 type value to be multiplied.
 * @param b             IQ21 type value to be multiplied.
 *
 * @return              IQ21 type result of the multiplication.    
 */
int32_t _IQ21mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 21);
}
/**
 * @brief Multiplies two values of IQ20 format.
 *
 * @param a             IQ20 type value to be multiplied.
 * @param b             IQ20 type value to be multiplied.
 *
 * @return              IQ20 type result of the multiplication.    
 */
int32_t _IQ20mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 20);
}
/**
 * @brief Multiplies two values of IQ19 format.
 *
 * @param a             IQ19 type value to be multiplied.
 * @param b             IQ19 type value to be multiplied.
 *
 * @return              IQ19 type result of the multiplication.    
 */
int32_t _IQ19mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 19);
}
/**
 * @brief Multiplies two values of IQ18 format.
 *
 * @param a             IQ18 type value to be multiplied.
 * @param b             IQ18 type value to be multiplied.
 *
 * @return              IQ18 type result of the multiplication.    
 */
int32_t _IQ18mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 18);
}
/**
 * @brief Multiplies two values of IQ17 format.
 *
 * @param a             IQ17 type value to be multiplied.
 * @param b             IQ17 type value to be multiplied.
 *
 * @return              IQ17 type result of the multiplication.    
 */
int32_t _IQ17mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 17);
}
/**
 * @brief Multiplies two values of IQ16 format.
 *
 * @param a             IQ16 type value to be multiplied.
 * @param b             IQ16 type value to be multiplied.
 *
 * @return              IQ16 type result of the multiplication.    
 */
int32_t _IQ16mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 16);
}
/**
 * @brief Multiplies two values of IQ15 format.
 *
 * @param a             IQ15 type value to be multiplied.
 * @param b             IQ15 type value to be multiplied.
 *
 * @return              IQ15 type result of the multiplication.    
 */
int32_t _IQ15mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 15);
}
/**
 * @brief Multiplies two values of IQ14 format.
 *
 * @param a             IQ14 type value to be multiplied.
 * @param b             IQ14 type value to be multiplied.
 *
 * @return              IQ14 type result of the multiplication.    
 */
int32_t _IQ14mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 14);
}
/**
 * @brief Multiplies two values of IQ13 format.
 *
 * @param a             IQ13 type value to be multiplied.
 * @param b             IQ13 type value to be multiplied.
 *
 * @return              IQ13 type result of the multiplication.    
 */
int32_t _IQ13mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 13);
}
/**
 * @brief Multiplies two values of IQ12 format.
 *
 * @param a             IQ12 type value to be multiplied.
 * @param b             IQ12 type value to be multiplied.
 *
 * @return              IQ12 type result of the multiplication.    
 */
int32_t _IQ12mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 12);
}
/**
 * @brief Multiplies two values of IQ11 format.
 *
 * @param a             IQ11 type value to be multiplied.
 * @param b             IQ11 type value to be multiplied.
 *
 * @return              IQ11 type result of the multiplication.    
 */
int32_t _IQ11mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 11);
}
/**
 * @brief Multiplies two values of IQ10 format.
 *
 * @param a             IQ10 type value to be multiplied.
 * @param b             IQ10 type value to be multiplied.
 *
 * @return              IQ10 type result of the multiplication.    
 */
int32_t _IQ10mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 10);
}
/**
 * @brief Multiplies two values of IQ9 format.
 *
 * @param a             IQ9 type value to be multiplied. 
 * @param b             IQ9 type value to be multiplied.
 *
 * @return              IQ9 type result of the multiplication.      
 */
int32_t _IQ9mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 9);
}
/**
 * @brief Multiplies two values of IQ8 format.
 *
 * @param a             IQ8 type value to be multiplied. 
 * @param b             IQ8 type value to be multiplied.
 *
 * @return              IQ8 type result of the multiplication.      
 */
int32_t _IQ8mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 8);
}
/**
 * @brief Multiplies two values of IQ7 format.
 *
 * @param a             IQ7 type value to be multiplied. 
 * @param b             IQ7 type value to be multiplied.
 *
 * @return              IQ7 type result of the multiplication.      
 */
int32_t _IQ7mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 7);
}
/**
 * @brief Multiplies two values of IQ6 format.
 *
 * @param a             IQ6 type value to be multiplied. 
 * @param b             IQ6 type value to be multiplied.
 *
 * @return              IQ6 type result of the multiplication.      
 */
int32_t _IQ6mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 6);
}
/**
 * @brief Multiplies two values of IQ5 format.
 *
 * @param a             IQ5 type value to be multiplied. 
 * @param b             IQ5 type value to be multiplied.
 *
 * @return              IQ5 type result of the multiplication.      
 */
int32_t _IQ5mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 5);
}
/**
 * @brief Multiplies two values of IQ4 format.
 *
 * @param a             IQ4 type value to be multiplied. 
 * @param b             IQ4 type value to be multiplied.
 *
 * @return              IQ4 type result of the multiplication.      
 */
int32_t _IQ4mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 4);
}
/**
 * @brief Multiplies two values of IQ3 format.
 *
 * @param a             IQ3 type value to be multiplied. 
 * @param b             IQ3 type value to be multiplied.
 *
 * @return              IQ3 type result of the multiplication.      
 */
int32_t _IQ3mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 3);
}
/**
 * @brief Multiplies two values of IQ2 format.
 *
 * @param a             IQ2 type value to be multiplied. 
 * @param b             IQ2 type value to be multiplied.
 *
 * @return              IQ2 type result of the multiplication.      
 */
int32_t _IQ2mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 2);
}
/**
 * @brief Multiplies two values of IQ1 format.
 *
 * @param a             IQ1 type value to be multiplied. 
 * @param b             IQ1 type value to be multiplied.
 *
 * @return              IQ1 type result of the multiplication.      
 */
int32_t _IQ1mpy(int32_t a, int32_t b)
{
    return __IQNmpy(a, b, 1);
}
