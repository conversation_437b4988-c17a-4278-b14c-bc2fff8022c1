#include "A_include.h"
/********************************************************************************************************************
*                  ------------------------------------
LEDS      A0
LEDR      B26
LEDG      B27
LEDB      B22
KEY0      A18
KEY1      B21
UART0TX   A10 
UART0RX   A11
*                  ------------------------------------
********************************************************************************************************************/

/*��ʱ������LEDB*��˸
*����KEY0�̰�����LEDR �����ر�
*����KEY1�̰�����LEDG �����ر�
*���ڴ�ӡhello
*�������ݣ������жϻػ�
*/
// 全局变量
static uint8_t servo_control_enabled = 0;  // 舵机控制使能标志

int main(void)
{
    SYSCFG_DL_init(); // 芯片资源初始化,由SysConfig工具自动生成
    UART_Init();      // 初始化UART（115200波特率，半双工模式）
    KEY_Init();       // 初始化按键
    Timer_Init();     // 初始化定时器
    Servo_Init();     // 初始化舵机

    // 延时等待系统稳定
    for(volatile uint32_t i = 0; i < 3200000; i++);  // 约100ms延时

    printf("MSPM0G3507 Servo Control System Started\r\n");
    printf("UART: 115200bps, Half-Duplex Mode\r\n");
    printf("KEY0: LED Control\r\n");
    printf("KEY1: Servo Control (ID1:90deg, ID2:120deg)\r\n");

    while(1)
    {
        // KEY0控制LED
        if(Key0_Flag==1){
            Key0_Flag=0;
            LEDR(1);
            printf("RED LED ON\r\n");
        }
        else if(Key0_Flag==2){
            Key0_Flag=0;
            LEDR(0);
            printf("RED LED OFF\r\n");
        }

        // KEY1控制舵机
        if(Key1_Flag==1){
            Key1_Flag=0;
            servo_control_enabled = 1;
            printf("Servo Control: Moving to target positions...\r\n");

            // 设置舵机到指定角度
            // ID1水平舵机设置到90度
            Servo_SetPositionWithTime(SERVO_ID_HORIZONTAL, 90.0f, 1000);
            printf("Servo ID1 (Horizontal) -> 90 degrees\r\n");

            // 延时确保第一个指令发送完成
            for(volatile uint32_t i = 0; i < 1600000; i++);  // 约50ms延时

            // ID2垂直舵机设置到120度
            Servo_SetPositionWithTime(SERVO_ID_VERTICAL, 120.0f, 1000);
            printf("Servo ID2 (Vertical) -> 120 degrees\r\n");

            LEDG(1);  // 点亮绿灯表示舵机控制完成
        }
        else if(Key1_Flag==2){
            Key1_Flag=0;
            LEDG(0);
            printf("GREEN LED OFF\r\n");
        }

        // 主循环延时
        for(volatile uint32_t i = 0; i < 320000; i++);  // 约10ms延时
    }
}
