#ifndef __SERVO_H_
#define __SERVO_H_

#include "A_include.h"

// 舵机协议定义
#define SERVO_HEADER                0x55        // 帧头
#define SERVO_CMD_MOVE_TIME_WRITE   0x01        // 指定时间写入位置指令
#define SERVO_CMD_MOVE_TIME_READ    0x02        // 指定时间读取位置指令
#define SERVO_CMD_MOVE_TIME_WAIT_WRITE 0x07     // 指定时间写入位置指令(等待)
#define SERVO_CMD_MOVE_TIME_WAIT_READ  0x08     // 指定时间读取位置指令(等待)
#define SERVO_CMD_MOVE_START        0x11        // 开始执行指令
#define SERVO_CMD_MOVE_STOP         0x12        // 停止执行指令
#define SERVO_CMD_ID_WRITE          0x13        // ID写入指令
#define SERVO_CMD_ID_READ           0x14        // ID读取指令
#define SERVO_CMD_ANGLE_OFFSET_ADJUST 0x17      // 角度偏移调整指令
#define SERVO_CMD_ANGLE_OFFSET_WRITE  0x18      // 角度偏移写入指令
#define SERVO_CMD_ANGLE_OFFSET_READ   0x19      // 角度偏移读取指令
#define SERVO_CMD_ANGLE_LIMIT_WRITE   0x20      // 角度限制写入指令
#define SERVO_CMD_ANGLE_LIMIT_READ    0x21      // 角度限制读取指令
#define SERVO_CMD_VIN_LIMIT_WRITE     0x22      // 输入电压限制写入指令
#define SERVO_CMD_VIN_LIMIT_READ      0x23      // 输入电压限制读取指令
#define SERVO_CMD_TEMP_MAX_LIMIT_WRITE 0x24     // 最高温度限制写入指令
#define SERVO_CMD_TEMP_MAX_LIMIT_READ  0x25     // 最高温度限制读取指令
#define SERVO_CMD_SERVO_OR_MOTOR_MODE_WRITE 0x29 // 舵机或电机模式写入指令
#define SERVO_CMD_SERVO_OR_MOTOR_MODE_READ  0x30 // 舵机或电机模式读取指令
#define SERVO_CMD_LOAD_OR_UNLOAD_WRITE 0x31     // 扭矩开关写入指令
#define SERVO_CMD_LOAD_OR_UNLOAD_READ  0x32     // 扭矩开关读取指令
#define SERVO_CMD_LED_CTRL_WRITE      0x33      // LED开关写入指令
#define SERVO_CMD_LED_CTRL_READ       0x34      // LED开关读取指令
#define SERVO_CMD_POS_READ            0x36      // 位置读取指令
#define SERVO_CMD_OR_MOTOR_MODE_WRITE 0x29      // 舵机或电机模式写入指令
#define SERVO_CMD_OR_MOTOR_MODE_READ  0x30      // 舵机或电机模式读取指令

// 舵机参数定义
#define SERVO_MAX_ANGLE             240.0f      // 最大角度
#define SERVO_MIN_ANGLE             0.0f        // 最小角度
#define SERVO_MAX_POSITION          1000        // 最大位置值
#define SERVO_MIN_POSITION          0           // 最小位置值

// 舵机ID定义
#define SERVO_ID_HORIZONTAL         1           // 水平舵机ID
#define SERVO_ID_VERTICAL           2           // 垂直舵机ID

// 默认角度定义
#define SERVO_DEFAULT_HORIZONTAL_ANGLE  90.0f   // 水平舵机默认角度
#define SERVO_DEFAULT_VERTICAL_ANGLE    90.0f   // 垂直舵机默认角度

// 默认运动时间
#define SERVO_DEFAULT_TIME          1000        // 默认运动时间1000ms

// 函数声明
void Servo_Init(void);
uint16_t Servo_AngleToPosition(float angle);
float Servo_PositionToAngle(uint16_t position);
uint8_t Servo_CalculateChecksum(uint8_t* data, uint8_t length);
void Servo_SendCommand(uint8_t id, uint8_t cmd, uint8_t* params, uint8_t param_len);
void Servo_SetPosition(uint8_t id, float angle);
void Servo_SetPositionWithTime(uint8_t id, float angle, uint16_t time_ms);
uint16_t Servo_ReadPosition(uint8_t id);
float Servo_ReadAngle(uint8_t id);
void Servo_SetHorizontalAngle(float angle);
void Servo_SetVerticalAngle(float angle);
void Servo_SetBothAngles(float horizontal_angle, float vertical_angle);
void Servo_SetDefaultPosition(void);
void Servo_Test(void);

#endif
