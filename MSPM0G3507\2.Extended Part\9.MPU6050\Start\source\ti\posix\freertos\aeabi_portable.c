/*
 * Copyright (c) 2017-2022 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR CO<PERSON><PERSON>QUE<PERSON>IAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ======== aeabi_portable.c ========
 */

#include <FreeRTOS.h>
#include <task.h>

#include "PTLS.h"

/*
 *  ======== posix_reent ========
 *  POSIX thread local reentrant structure
 */
typedef struct {
    int     errnum; /* Note: errno is #defined by tiarmclang so cannot be used */
} posix_reent;

static posix_reent main_reent; /* used only while in main thread */


/*
 *  ======== __aeabi_errno_addr ========
 *  Return the address of thread-local errno data location
 *
 *  This function replaces the same from the GNU toolchain.
 *  It provides a thread-local errno memory location.
 */
volatile int *__aeabi_errno_addr(void)
{
    posix_reent *reent;
    void *buf;

    /* when running in main thread, use private reent storage */
    if (xTaskGetSchedulerState() == taskSCHEDULER_NOT_STARTED) {
        reent = &main_reent;
    }
    else {
        /* get thread local storage buffer */
        reent = (posix_reent *)PTLS_getBuf();

        /* allocate buffer if needed */
        if (reent == NULL) {
            buf = pvPortMalloc(sizeof(posix_reent));
            if (buf != NULL) {
                PTLS_setBuf(buf);
                reent = (posix_reent *)buf;
            }
            else {
                /* out of memory: fall back to using private storage */
                reent = &main_reent;
            }
        }
    }

    return (&reent->errnum);
}
