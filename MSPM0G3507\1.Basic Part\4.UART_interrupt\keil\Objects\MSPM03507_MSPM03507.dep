Dependencies for Project 'MSPM03507', Target 'MSPM03507': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (..\USER\main.c)(0x6888CB7A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MMD)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\ncontroller.syscfg)(0x668D355D)()
F (..\ti_msp_dl_config.c)(0x6888CAEA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
F (..\ti_msp_dl_config.h)(0x6888CAD2)()
F (.\startup_mspm0g350x_uvision.s)(0x662FCA6B)(--cpu Cortex-M0+ -g --diag_suppress=A1950W

--pd "__UVISION_VERSION SETA 540"

--pd "__MSPM0G3507__ SETA 1"

--list .\listings\startup_mspm0g350x_uvision.lst

--xref -o .\objects\startup_mspm0g350x_uvision.o

--depend .\objects\startup_mspm0g350x_uvision.d)
F (..\USER\A_include.h)(0x6888CB45)()
F (..\hardware\LED\LED.c)(0x668D0842)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/led.o -MMD)
I (..\USER\..\HARDWARE\LED\led.h)(0x668D0F4A)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\hardware\KEY\KEY.c)(0x668F8B98)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/key.o -MMD)
I (..\hardware\KEY\key.h)(0x668D0A11)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\hardware\SERVO\servo.c)(0x6888CB38)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/servo.o -MMD)
I (..\hardware\SERVO\servo.h)(0x6888CB14)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\hardware\SERVO\servo.h)(0x6888CB14)()
F (..\software\Timer\Timer.c)(0x668F8BA4)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/timer.o -MMD)
I (..\software\Timer\timer.h)(0x668D0E8C)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\software\UART\UART.c)(0x6888CABE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/uart.o -MMD)
I (..\software\UART\uart.h)(0x6888CA0E)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\software\delay\delay.c)(0x668D04AC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/delay.o -MMD)
I (..\software\delay\delay.h)(0x668D04AC)
I (..\USER\A_include.h)(0x6888CB45)
I (..\ti_msp_dl_config.h)(0x6888CAD2)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_interrupt.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_systick.h)(0x662FCA6A)
I (..\USER\..\software\delay\delay.h)(0x668D04AC)
I (..\USER\..\software\Timer\Timer.h)(0x668D0E8C)
I (..\USER\..\software\uart\uart.h)(0x6888CA0E)
I (..\USER\..\HARDWARE\LED\LED.h)(0x668D0F4A)
I (..\USER\..\HARDWARE\key\key.h)(0x668D0A11)
I (..\USER\..\HARDWARE\SERVO\servo.h)(0x6888CB14)
F (..\Start\source\ti\driverlib\dl_adc12.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MMD)
I (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_adc12.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_aes.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MMD)
I (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_aes.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_aesadv.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MMD)
I (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_aesadv.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_common.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MMD)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_comp.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_crc.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MMD)
I (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_crc.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_crcp.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MMD)
I (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_crcp.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_dac12.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MMD)
I (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_dac12.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_dma.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MMD)
I (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_dma.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_flashctl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MMD)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_core.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\dl_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_flashctl.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_gpamp.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_gpio.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_i2c.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MMD)
I (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_i2c.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_iwdt.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_keystorectl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MMD)
I (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_keystorectl.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_lcd.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MMD)
I (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_lcd.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_lfss.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MMD)
I (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_lfss.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_mathacl.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MMD)
I (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_mathacl.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_mcan.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MMD)
I (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_mcan.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_opa.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MMD)
I (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_opa.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_rtc.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_rtc_a.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_rtc_common.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MMD)
I (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_rtc_common.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_scratchpad.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_spi.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MMD)
I (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_spi.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_tamperio.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_timer.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MMD)
I (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)
I (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)
F (..\Start\source\ti\driverlib\dl_timer.h)(0x6640DF41)()
F (..\Start\source\ti\driverlib\dl_timera.h)(0x6640DF41)()
F (..\Start\source\ti\driverlib\dl_timerg.h)(0x6640DF42)()
F (..\Start\source\ti\driverlib\dl_trng.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MMD)
I (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_trng.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_uart.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MMD)
I (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_uart.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_uart_extend.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_uart_main.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_vref.c)(0x662FCA6A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../Start/source/third_party/CMSIS/Core/Include -I ../Start/source -I ../ -I ../USER

-D__UVISION_VERSION="540" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MMD)
I (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\msp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\DeviceFamily.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\m0p\mspm0g350x.h)(0x6640DF41)
I (..\Start\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_adc12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_aes.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_comp.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_crc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dac12.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_dma.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gpio.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_i2c.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_iomux.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_mcan.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_oa.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_rtc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_spi.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_trng.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_uart.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_vref.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wuc.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x662FCA6A)
I (..\Start\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x662FCA6A)
I (..\Start\source\ti\driverlib\dl_common.h)(0x662FCA6A)
F (..\Start\source\ti\driverlib\dl_vref.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\dl_wwdt.h)(0x662FCA6A)()
F (..\Start\source\ti\driverlib\driverlib.h)(0x662FCA6A)()
