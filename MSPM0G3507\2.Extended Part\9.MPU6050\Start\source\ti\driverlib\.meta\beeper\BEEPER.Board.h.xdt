%%{
/*
 * Copyright (c) 2018 Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

/*
 *  ======== BEEPER.Board.h.xdt ========
 */

    /* args[] passed by /ti/driverlib/templates/Board.h.xdt */
    let BEEPER = args[0];
    let content = args[1];

    /* get ti/drivers common utility functions */
    let Common = system.getScript("/ti/driverlib/Common.js");

    let stat = BEEPER.$static;

    switch(content) {
        case "Define":
            printDefine();
            break;
        case "Declare":
            printDeclare();
            break;
        default:
            /* do nothing */
            break;
    }
%%}
%
% function printDefine() {
/* Defines for BEEPER */
%   let outPackagePin;
%   let outPinName = "beeperPin";
%   let outPinCM;
%   let outIOmux;
%   let outGpioName;
%   let outGpioPin;
%   let outPort;
%       let gpioStr2 = "GPIO_BEEPER";
%   if(stat.beeperEnable) {
%       outPackagePin = stat.peripheral[outPinName].$solution.packagePinName;
%       outPinCM = Common.getPinCM(outPackagePin,stat,"OUT");
%       outGpioName = system.deviceData.devicePins[outPackagePin].mux.muxSetting[0].peripheralPin.peripheralName;
%       outGpioPin = Common.getGPIONumberMultiPad(outPackagePin,stat,"OUT");
%       outPort = Common.getGPIOPortMultiPad(outPackagePin,stat,"OUT");
%   let outPortStr = "#define "+gpioStr2+"_OUT_PORT";
`outPortStr.padEnd(40," ")+outPort.padStart(40, " ")`
%   let outPinStr = "#define "+gpioStr2+"_OUT_PIN";
%   let outPinStr2 = "DL_GPIO_PIN_"+outGpioPin;
`outPinStr.padEnd(40," ")+outPinStr2.padStart(40," ")`
%   let outIOmuxStr = "#define "+gpioStr2+"_IOMUX_OUT";
%   let outIOmuxStr2 = "(IOMUX_PINCM"+outPinCM.toString()+")";
`outIOmuxStr.padEnd(40," ")+outIOmuxStr2.padStart(40," ")`
%   let outFuncStr = "#define "+gpioStr2+"_IOMUX_OUT_FUNC";
%   let outFuncStr2 = "IOMUX_PINCM"+outPinCM+"_PF_SYSCTL_BEEPER";
`outFuncStr.padEnd(40, " ")+outFuncStr2.padStart(40, " ")`
%   }//if(stat.enableOutput)
% }
%
% function printDeclare() {
void SYSCFG_DL_BEEPER_init(void);
% }
